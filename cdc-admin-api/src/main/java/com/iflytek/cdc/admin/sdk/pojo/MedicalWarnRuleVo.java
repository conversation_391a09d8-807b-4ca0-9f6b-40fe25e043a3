package com.iflytek.cdc.admin.sdk.pojo;


import lombok.Data;

@Data
public class MedicalWarnRuleVo {

    /**
     * id
     */
    private String id;

    /**
     * 规则所属区域代码
     */
    private String regionId;

    /**
     *病种编码
     */
    private String diseaseCode;

    /**
     *病种名称
     */
    private String diseaseName;

    /**
     *时间范围单位(1=天，2=周，3=月)
     */
    private String timeUnit;

    /**
     *时间运算规则(1=小于等于，2=小于，3=大于等于，4=大于，5=等于)
     */
    private String timeOperate;

    /**
     *时间范围数量
     */
    private Integer timeScope;

    /**
     *监测对象(1=医疗机构，2=学校，3=街道)
     */
    private String monitorObject;

    /**
     *病例运算规则(1=小于等于，2=小于 3=大于等于，4=大于，5=等于)
     */
    private String medicalOperate;

    /**
     *病例数量
     */
    private Integer medicalCount;

    /**
     *病例属性(1=确诊病例，2=疑似病例，9=死亡病例)
     */

    private String medicalAttribute;

    /**
     *警示类型(1=预警信号，2=上报提示)
     */
    private String warnType;

    /**
     *生成规则json
     */
    private String ruleJson;

    /**
     *当前病种是否启用（  0:否   1:是）
     */
    private String ruleStatus;
}
