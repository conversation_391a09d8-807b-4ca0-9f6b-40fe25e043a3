package com.iflytek.cdc.admin.sdk.entity;

import com.iflytek.cdc.admin.sdk.pojo.PackageVersionParam;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName ClientUpgradeFilter
 * @Description 升级服务地址
 * <AUTHOR>
 * @Date 2021/8/26 16:34
 * @Version 1.0
 */

@Data
public class ClientUpgradeFilter {

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 客户端类型(0:上报卡  1:防保科)
     */
    private String clientType;

    /**
     * 当前版本号
     */
    private String currentVersion;

    /**
     * 程序集名称
     */
    private String assembly;

}
