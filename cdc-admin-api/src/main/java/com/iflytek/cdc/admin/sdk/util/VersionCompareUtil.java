package com.iflytek.cdc.admin.sdk.util;




import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class VersionCompareUtil {

    private VersionCompareUtil(){}

    /**
     * @deprecated
     * 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     * 版本命名规则(x代表一位数字,y代表任意位数字符串) x.x.x.xxx 或 x.x.x.xxx-y
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return int
     */
    @Deprecated
    public static int compareVersion(String version1, String version2) {
        if (StringUtils.isBlank(version1) || StringUtils.isBlank(version2)) {

        }
        //注意此处为正则匹配，不能用"."；
        String[] versionArray1 = version1.split("\\.");
        String[] versionArray2 = version2.split("\\.");
        int idx = 0;
        //取最小长度值
        int minLength = Math.min(versionArray1.length, versionArray2.length);
        int diff = 0;
        //先比较前三位
        while (idx < minLength - 1
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {
            ++idx;
        }
        //前三位相同时,比较最后一位,
        // 最后一个小数点后,补两个零后比较,最后一位默认3位
        if (diff == 0) {
            diff = (versionArray1[idx] + "00").substring(0, 3).compareTo((versionArray2[idx] + "00").substring(0, 3));
        }
        //如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }

    /**
     * 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     * 版本命名规则 x.x.x.xxx 或 x.x.x.xxx
     */
    public static int compareVersionV2(String compareVersion, String currentVersion) {
        if (StringUtils.isBlank(compareVersion) || StringUtils.isBlank(currentVersion)) {

        }
        //注意此处为正则匹配，不能用"."；
        String[] versionArray1 = compareVersion.split("\\.");
        String[] versionArray2 = currentVersion.split("\\.");
        int[] versionArray1Int = new int[0];
        int[] versionArray2Int = new int[0];
        try {
            versionArray1Int = Arrays.asList(versionArray1).stream().mapToInt(Integer::parseInt)
                    .toArray();
            versionArray2Int = Arrays.asList(versionArray2).stream().mapToInt(Integer::parseInt)
                    .toArray();
        } catch (Exception e) {

        }

        //取最小长度值
        int minLength = Math.min(versionArray1Int.length, versionArray2Int.length);
        int idx = 0;
        int diff = 0;
        //先比较前三位
        while (idx < minLength - 1
                && (diff = versionArray1Int[idx] - (versionArray2Int[idx])) == 0) {
            ++idx;
        }
        //前三位相同时,比较最后一位,
        if (diff == 0) {
            diff = versionArray1Int[idx] - versionArray2Int[idx];
        }
        //如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }



    public static String sort(List<String> list){
        if (list!=null&&list.size()==1){
            return list.get(0);
        }
        List<String> newList = new ArrayList<String>();
        if(list!=null&&list.size()>0){
            //取出list中第一条记录对应版本号的第一位
            String version0 = list.get(0).split("\\.")[0];
            for (String v: list){
                if (Integer.parseInt(v.split("\\.")[0])==Integer.parseInt(version0)){
                    //如果第一位相等，则放入newList中
                    newList.add(v);
                }
                if(Integer.parseInt(v.split("\\.")[0])>Integer.parseInt(version0)){
                    newList.clear();
                    newList.add(v);
                    //更新version
                    version0 = v.split("\\.")[0];
                }
            }
            List<String> newList1 = new ArrayList<String>();
            if(newList!=null&&newList.size()>1){
                //取出list中第一条记录对应版本号的第二位
                String version1 = newList.get(0).split("\\.")[1];
                for (String v: newList){
                    if (Integer.parseInt(v.split("\\.")[1])==Integer.parseInt(version1)){
                        //如果第二位相等，则放入newList中
                        newList1.add(v);
                    }
                    if(Integer.parseInt(v.split("\\.")[1])>Integer.parseInt(version1)){
                        newList1.clear();
                        newList1.add(v);
                        //更新version
                        version1 = v.split("\\.")[1];
                    }
                }
                newList = newList1;
                List<String> newList2 = new ArrayList<String>();
                if(newList1!=null&&newList1.size()>1){
                    //取出list中第一条记录对应版本号的第三位
                    String version2 = newList1.get(0).split("\\.")[2];
                    for (String v: newList1){
                        if (Integer.parseInt(v.split("\\.")[2])==Integer.parseInt(version2)){
                            //如果第一位相等，则放入newList中
                            newList2.add(v);
                        }
                        if(Integer.parseInt(v.split("\\.")[2])>Integer.parseInt(version2)){
                            newList2.clear();
                            newList2.add(v);
                            //更新version
                            version2 = v.split("\\.")[2];
                        }
                    }
                    newList = newList2;

                    List<String> newList3 = new ArrayList<String>();
                    if(newList2!=null&&newList2.size()>1){
                        //取出list中第一条记录对应版本号的第四位
                        String version3 = newList2.get(0).split("\\.")[3];
                        for (String v: newList2){
                            if (Integer.parseInt(v.split("\\.")[3])==Integer.parseInt(version3)){
                                //如果第一位相等，则放入newList中
                                newList3.add(v);
                            }
                            if(Integer.parseInt(v.split("\\.")[3])>Integer.parseInt(version3)){
                                newList3.clear();
                                newList3.add(v);
                                //更新version
                                version3 = v.split("\\.")[3];
                            }
                        }
                        newList = newList3;
                    }
                }
            }
        }

        return newList.get(0);
    }

}

