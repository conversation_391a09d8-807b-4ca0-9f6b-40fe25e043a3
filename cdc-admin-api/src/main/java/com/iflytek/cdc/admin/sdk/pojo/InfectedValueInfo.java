package com.iflytek.cdc.admin.sdk.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/9/10 15:49
 **/
@Data
public class InfectedValueInfo {

    /**
     * 传染病类别代码 1、甲类；2、乙类；3、丙类；9、其他
     */
    private String infectedTypeCode;

    /**
     * 传染病类别名称
     */
    private String infectedTypeName;

    /**
     * 传染病业务名称代码
     */
    private String infectedCode;

    /**
     * 传染病业务名称
     */
    private String infectedName;

    /**
     * 传染病名称代码
     */
    private String infectedSubCode;

    /**
     * 传染病名称
     */
    private String infectedSubName;
}
