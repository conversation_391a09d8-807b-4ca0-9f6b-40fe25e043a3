create table app.tb_cdcmr_disease_permission (
  id character varying(64) primary key not null, -- 主键
  org_id character varying(64), -- 机构id
  subsystem_code character varying(64), -- 系统code
  infected_disease_arr json, -- 传染病疾病权限
  syndrome_disease_arr json, -- 症候群疾病权限
  create_time timestamp without time zone, -- 创建时间
  update_time timestamp without time zone, -- 更新时间
  creator_id character varying(100), -- 创建人id
  creator character varying(100), -- 创建人
  updater_id character varying(100), -- 更新人id
  updater character varying(100), -- 更新人
  infected_all_flag character varying(2), -- 传染病全部标识 0-全部 1-非全部
  syndrome_all_flag character varying(2) -- 症候群全部标识 0-全部 1-非全部
);
comment on table app.tb_cdcmr_disease_permission is '疾病权限表';
comment on column app.tb_cdcmr_disease_permission.id is '主键';
comment on column app.tb_cdcmr_disease_permission.org_id is '机构id';
comment on column app.tb_cdcmr_disease_permission.subsystem_code is '系统code';
comment on column app.tb_cdcmr_disease_permission.infected_disease_arr is '传染病疾病权限';
comment on column app.tb_cdcmr_disease_permission.syndrome_disease_arr is '症候群疾病权限';
comment on column app.tb_cdcmr_disease_permission.create_time is '创建时间';
comment on column app.tb_cdcmr_disease_permission.update_time is '更新时间';
comment on column app.tb_cdcmr_disease_permission.creator_id is '创建人id';
comment on column app.tb_cdcmr_disease_permission.creator is '创建人';
comment on column app.tb_cdcmr_disease_permission.updater_id is '更新人id';
comment on column app.tb_cdcmr_disease_permission.updater is '更新人';
comment on column app.tb_cdcmr_disease_permission.infected_all_flag is '传染病全部标识 0-全部 1-非全部';
comment on column app.tb_cdcmr_disease_permission.syndrome_all_flag is '症候群全部标识 0-全部 1-非全部';

create table app.tb_cdcmr_business_permission (
  id character varying(64) primary key not null, -- 主键
  org_id character varying(64), -- 机构id
  subsystem_code character varying(64), -- 系统code
  business_type_arr json, -- 业务权限类型
  create_time timestamp without time zone, -- 创建时间
  update_time timestamp without time zone, -- 更新时间
  creator_id character varying(100), -- 创建人id
  creator character varying(100), -- 创建人
  updater_id character varying(100), -- 更新人id
  updater character varying(100) -- 更新人
);
comment on table app.tb_cdcmr_business_permission is '业务权限表';
comment on column app.tb_cdcmr_business_permission.id is '主键';
comment on column app.tb_cdcmr_business_permission.org_id is '机构id';
comment on column app.tb_cdcmr_business_permission.subsystem_code is '系统code';
comment on column app.tb_cdcmr_business_permission.business_type_arr is '业务权限类型';
comment on column app.tb_cdcmr_business_permission.create_time is '创建时间';
comment on column app.tb_cdcmr_business_permission.update_time is '更新时间';
comment on column app.tb_cdcmr_business_permission.creator_id is '创建人id';
comment on column app.tb_cdcmr_business_permission.creator is '创建人';
comment on column app.tb_cdcmr_business_permission.updater_id is '更新人id';
comment on column app.tb_cdcmr_business_permission.updater is '更新人';

