import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.job.UnCheckSignalNotifyJob;
import com.iflytek.cdc.admin.service.province.WarningChargePersonService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class SignalPushTest {

    @Resource
    private WarningChargePersonService warningChargePersonService;

    @Resource
    private UnCheckSignalNotifyJob unCheckSignalNotifyJob;

    @Test
    public void test(){
        warningChargePersonService.scanSignalPushConfiguration();
    }

    @Test
    public void test2(){
        try {
            unCheckSignalNotifyJob.execute("340000");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
