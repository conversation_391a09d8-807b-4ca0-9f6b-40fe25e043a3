package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityQueryDTO;
import com.iflytek.cdc.admin.entity.BizProcessConfig;
import com.iflytek.cdc.admin.vo.BizProcessCheckAuthorityVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface BizProcessConfigMapper extends BaseMapper<BizProcessConfig> {
    BizProcessCheckAuthorityVO queryByOrgIdAndDiseaseCode(BizProcessCheckAuthorityDTO dto);

    List<BizProcessCheckAuthorityVO> queryList(BizProcessCheckAuthorityQueryDTO dto);

    void updateCheckPerson(List<BizProcessCheckAuthorityVO> noPermissionList);
}
