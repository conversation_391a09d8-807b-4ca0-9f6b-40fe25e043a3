package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.PopulationDataHistoricalInfoQueryDTO;
import com.iflytek.cdc.admin.dto.TbCdcmrPopulationDataInfoQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo;
import com.iflytek.cdc.admin.vo.PopulationDataInfoVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbCdcmrPopulationDataInfoMapper {

    /**
     * 新增
     **/
    int insert(TbCdcmrPopulationDataInfo tbCdcmrPopulationDataInfo);

    /**
     * 批量新增
     */
    int batchInsert(List<TbCdcmrPopulationDataInfo> entities);

    /**
     * 刪除
     **/
    int delete(String id);

    /**
     * 更新
     **/
    int update(TbCdcmrPopulationDataInfo tbCdcmrPopulationDataInfo);

    /**
     * 查询 根据主键 id 查询
     **/
    TbCdcmrPopulationDataInfo load(String id);

    /**
     * 查询 分页查询
     **/
    List<TbCdcmrPopulationDataInfo> pageList(TbCdcmrPopulationDataInfoQueryDTO queryDTO);

    /**
     * 根据区域编码查询
     **/
    List<PopulationDataInfoVO> listByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO);

    /**
     * 根据区域编码查询总计
     **/
    PopulationDataInfoVO statByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO);


    List<PopulationDataInfoVO> listByArea(TbCdcmrPopulationDataInfoQueryDTO queryDTO);

    void updateHistoricalStatistics(List<TbCdcmrPopulationDataInfo> inputs);

    List<TbCdcmrPopulationDataInfo> listByAreaAndDate(PopulationDataHistoricalInfoQueryDTO queryDTO);

    List<PopulationDataInfoVO> listByEachAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO);
}
