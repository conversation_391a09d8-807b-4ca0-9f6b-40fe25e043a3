package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.PoisoningWarnRuleExportDataDto;
import com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrPoisoningWarnRuleMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrPoisoningWarnRule record);

    int insertSelective(TbCdcmrPoisoningWarnRule record);

    TbCdcmrPoisoningWarnRule selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrPoisoningWarnRule record);

    int updateByPrimaryKey(TbCdcmrPoisoningWarnRule record);

    List<TbCdcmrPoisoningWarnRule> getRuleListByWarnId(String warnId);

    List<TbCdcmrPoisoningWarnRule> getAllRuleList();

    List<PoisoningWarnRuleExportDataDto> getExportData();

    int deleteOtherByIds(@Param("idList") List<String> idList, @Param("warnId") String warnId);

    int upsertRules(List<TbCdcmrPoisoningWarnRule> recordList);

}