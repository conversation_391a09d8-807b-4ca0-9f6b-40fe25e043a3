package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.entity.DiseasePermission;
import lombok.Data;

import java.util.List;


@Data
public class BizDiseasePermissionQueryDTO {

    private String orgId;

    private String subSystemCode;

    private String id;

    private List<DiseasePermission.DiseaseInfoData> infectedDiseaseArr;

    private List<DiseasePermission.DiseaseInfoData> syndromeDiseaseArr;

    private String infectedAllFlag;

    private String syndromeAllFlag;

    private String infectedDiseaseInfo;

    private String syndromeDiseaseInfo;
}
