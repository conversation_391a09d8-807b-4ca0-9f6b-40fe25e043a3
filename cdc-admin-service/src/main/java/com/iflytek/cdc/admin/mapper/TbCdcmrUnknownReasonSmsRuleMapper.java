package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcmrUnknownReasonSmsRuleMapper {
    int insert(List<TbCdcmrUnknownReasonSmsRule> record);

    int updateByPrimaryKeySelective(List<TbCdcmrUnknownReasonSmsRule> recordList);

    int deleteByUnknownReasonCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                                      @Param("loginUserId") String loginUserId, @Param("unknownReasonCodeList") List<String> unknownReasonCodeList);

    List<TbCdcmrUnknownReasonSmsRule> getListByLoginUserId(@Param("loginUserId") String loginUserId);

    List<TbCdcmrUnknownReasonSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                            @Param("loginUserId") String loginUserId);


}