package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.BizDiseasePermissionQueryDTO;
import com.iflytek.cdc.admin.dto.BizPermissionQueryDTO;
import com.iflytek.cdc.admin.entity.BusinessPermission;
import com.iflytek.cdc.admin.entity.DiseasePermission;
import com.iflytek.cdc.admin.model.mr.vo.DeptUserPermissionVO;
import com.iflytek.cdc.admin.model.mr.vo.PermissionDiseaseInfoDataVO;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;

import java.util.List;

public interface BusinessProcessPermissionService {
    BusinessPermission queryBizPermission(BizPermissionQueryDTO dto,String loginUserId);

    BusinessPermission updateBizConfig(BizPermissionQueryDTO dto, String loginUserId);

    DiseasePermission queryDiseaseConfig(BizDiseasePermissionQueryDTO dto, String loginUserId);

    DiseasePermission updateDiseaseConfig(BizDiseasePermissionQueryDTO dto, String loginUserId);

    DeptUserPermissionVO queryDeptUserPermission(String subSystemCode,String loginUserId);

    PermissionDiseaseInfoDataVO queryPermissionDiseaseInfoData(String subSystemCode, String loginUserId);

    List<TreeNode> queryPermissionInfectedInfoData(String loginUserId);

    List<SyndromeDiseaseInfoVO> queryPermissionSyndromeInfoData(String loginUserId);

}
