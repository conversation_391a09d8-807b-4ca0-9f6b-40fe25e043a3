package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.PreventionControlGradeConfigVO;
import com.iflytek.cdc.admin.dto.PreventionControlQueryDto;
import com.iflytek.cdc.admin.dto.PreventionControlWarnDto;
import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlWarn;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrPreventionControlWarnMapper {

    List<PreventionControlWarnDto> getList(PreventionControlQueryDto dto);

    void updateByPrimaryKeySelective(TbCdcmrPreventionControlWarn tbCdcmrPreventionControlWarn);

    void updateStatusByPrimaryKey(TbCdcmrPreventionControlWarn tbCdcmrPreventionControlWarn);

    TbCdcmrPreventionControlWarn selectByPrimaryKey(String warnId);

    List<PreventionControlWarnDto> getAllList(String preventionControlCode);

    List<PreventionControlGradeConfigVO> queryPreventionControlInfoByGrade(@Param("preventionControlName") String preventionControlName, @Param("status") Integer status);

}