package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName(schema ="app",  value = "tb_cdcmr_biz_process_config")
public class BizProcessConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    public static final String TB_NAME="tb_cdcmr_biz_process_config";

    private String id;

    private String orgId;

    private String configId;

    private String subSystemCode;

    private String divideType;

    private String creator;

    private String creatorId;

    private Date createTime;

    private String updater;

    private String updaterId;

    private Date updateTime;


    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;
}
