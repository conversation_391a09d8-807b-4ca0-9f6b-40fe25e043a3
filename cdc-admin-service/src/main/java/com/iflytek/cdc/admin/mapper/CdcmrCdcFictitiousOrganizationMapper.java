package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.CdcmrCdcFictitiousOrganization;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CdcmrCdcFictitiousOrganizationMapper {
    int deleteByPrimaryKey(String id);

    int insert(CdcmrCdcFictitiousOrganization record);

    int insertSelective(CdcmrCdcFictitiousOrganization record);

    CdcmrCdcFictitiousOrganization selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CdcmrCdcFictitiousOrganization record);

    int updateByPrimaryKey(CdcmrCdcFictitiousOrganization record);

    List<CdcmrCdcFictitiousOrganization> selectByProperty(CdcmrCdcFictitiousOrganization record);
}