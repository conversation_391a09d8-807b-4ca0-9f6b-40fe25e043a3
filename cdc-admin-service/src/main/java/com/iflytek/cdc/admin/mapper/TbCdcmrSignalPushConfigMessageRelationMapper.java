package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfigMessageRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrSignalPushConfigMessageRelationMapper extends BaseMapper<TbCdcmrSignalPushConfigMessageRelation> {
    List<TbCdcmrSignalPushConfigMessageRelation> listBySignalPushConfigurationId(String id);

    Integer insertBatch(List<TbCdcmrSignalPushConfigMessageRelation> relationList);


    List<TbCdcmrSignalPushConfigMessageRelation> listByTaskAndRecervierId(String taskId, String receiverId);

    void updateStatusOffByReceiverAndTaskIdAndConfig(@Param("dtos") List<SignalPushRuleDTO> dtos);
}