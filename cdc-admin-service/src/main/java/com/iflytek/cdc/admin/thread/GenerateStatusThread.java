package com.iflytek.cdc.admin.thread;

import lombok.extern.slf4j.Slf4j;


import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @ClassName GenerateStatusThread
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/9 10:31
 * @Version 1.0
 */

@Slf4j
public class GenerateStatusThread implements Runnable{

    /**
     * 任务实例
     */
    public Future<Status> future;

    /**
     * 最大等待时间 设置为60s，超过则报超时错误（减少超时时间监控，防止进程假死）
     */
    public static final int MAX_WAIT = 60000*3;

    /**
     * redis文件管理器
     */
    public RedisHandlerService redisHandlerService;

    /**
     * 具体需要执行的任务类
     */
    public GenerateAsyncTaskCallable taskCallable;

    public  GenerateStatusThread(Future<Status> future, GenerateAsyncTaskCallable taskCallable, RedisHandlerService redisHandlerService) {
        this.future = future;
        this.taskCallable = taskCallable;
        this.redisHandlerService = redisHandlerService;
    }

    @Override
    public void run() {
        Status result = Status.ERROR;
        String reason = "";
        //todo 这边设置超时时间，如果超时则直接进入异常捕获中
        try {
            future.get(MAX_WAIT, TimeUnit.MILLISECONDS);
            result = future.get();
        } catch (InterruptedException e) {
            result = Status.ERROR;
            log.error("#######生成异常" + e.getMessage());
            reason = e.getMessage();
        } catch (ExecutionException e) {
            result = Status.ERROR;
            log.error("#######生成异常" + e.getMessage());
            reason = e.getMessage();
        } catch (TimeoutException e) {
            result = Status.ERROR;
            reason = "同步MDM数据，请稍后重试";
        } finally {
            //todo 更新状态
            redisHandlerService.setStatus(taskCallable.getBatchId(),result);
            redisHandlerService.setStatus(taskCallable.getCode(),Status.DONE);
            log.warn("GenerateStatusThread $$$$$ 同步MDM数据,$$$$$状态result,{}，{}",  result,reason);
        }
    }
}
