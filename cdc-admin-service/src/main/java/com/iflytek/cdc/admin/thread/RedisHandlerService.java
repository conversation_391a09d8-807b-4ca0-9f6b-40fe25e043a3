package com.iflytek.cdc.admin.thread;

import cn.hutool.json.JSONUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName RedisHandlerService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/9 10:19
 * @Version 1.0
 */

@Service
public class RedisHandlerService {

    @Resource
    private  RedisTemplate<String, Object> redisTemplate;

    public void setStatus(String batchId,Status status){
        redisTemplate.opsForValue().set(batchId, JSONUtil.toJsonStr(status.toString()),3L, TimeUnit.MINUTES);
    }

    public String getStatus(String batchId){
       String status = (String) redisTemplate.opsForValue().get(batchId);
       return status;
    }
}
