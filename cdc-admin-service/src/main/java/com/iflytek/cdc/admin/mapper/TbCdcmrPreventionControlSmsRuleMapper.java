package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcmrPreventionControlSmsRuleMapper {
    int insert(List<TbCdcmrPreventionControlSmsRule> record);

    int updateByPrimaryKeySelective(List<TbCdcmrPreventionControlSmsRule> recordList);

    int deleteByPreventionControlCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                                      @Param("loginUserId") String loginUserId, @Param("preventionControlCodeList") List<String> preventionControlCodeList);

    List<TbCdcmrPreventionControlSmsRule> getListByLoginUserId(@Param("loginUserId") String loginUserId);

    List<TbCdcmrPreventionControlSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                            @Param("loginUserId") String loginUserId);


}