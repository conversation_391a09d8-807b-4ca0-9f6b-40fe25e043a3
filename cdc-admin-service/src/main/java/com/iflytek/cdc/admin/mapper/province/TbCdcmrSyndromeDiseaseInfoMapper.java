package com.iflytek.cdc.admin.mapper.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseInfo;
import com.iflytek.cdc.admin.model.mr.dto.CommonMasterData;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrSyndromeDiseaseInfoMapper {

    /**
     * 查询症候群结构树
     * */
    List<SyndromeDiseaseInfoVO> getSyndromeTreeInfo();

    /**
     * 更新数据
     *
     * @param diseaseInfo 实例对象
     * @return 影响行数
     */
    int update(TbCdcmrSyndromeDiseaseInfo diseaseInfo);

    /**
     * 批量插入数据
     * */
    void insertBatch(@Param("list") List<TbCdcmrSyndromeDiseaseInfo> list);

    /**
     * 根据id查询
     */
    SyndromeDiseaseInfoVO loadById(String id);

    /**
     * 根据编码查询
     */
    SyndromeDiseaseInfoVO loadByCode(String code);

    /**
     * 编辑症候群信息
     * */
    void insertOrUpdate(List<TbCdcmrSyndromeDiseaseInfo> list);

    /**
     * 更新该条数据删除状态
     *
     * @param id 主键
     * @return 影响行数
     */
    int updateDeleteFlagById(String id);

    List<String> getAllDiseaseCodeBy();

    /**
     * 所有症候群信息
     */
    List<TbCdcmrSyndromeDiseaseInfo> listAll();

    String getSyndromeDiseaseCodeByName(@Param("diseaseName") String diseaseName);

    /**
     * 根据 id列表 批量查询症候群信息
     */
    List<SyndromeDiseaseInfoVO> loadByIdList(@Param("idList") List<String> idList);

    /**
     * 根据id查询改症候群下所有子症候群
     * */
    List<TbCdcmrSyndromeDiseaseInfo> getSyndromeDiseaseInfoByIds(@Param("ids") List<String> ids);

    /**
     * 根据id软删除疾病
     * */
    void updateDeleteFlagByIds(@Param("ids") List<String> ids,
                               @Param("loginUserId") String loginUserId,
                               @Param("loginUserName") String loginUserName);

    /**
     * 将该疾病下所有子类的 父类id置空
     * */
    void updateSubDiseaseParent(TbCdcmrSyndromeDiseaseInfo info,
                                @Param("loginUserId") String loginUserId,
                                @Param("loginUserName") String loginUserName);

    /**
     * 根据id查询疾病
     * */
    TbCdcmrSyndromeDiseaseInfo getDiseaseInfoById(@Param("id") String id);

    /**
     * 通过code获取疾病信息
     * */
    CommonMasterData getDiseaseInfoByCode(@Param("masterDataCode") String masterDataCode);

    List<SyndromeDiseaseInfoVO> getSyndromeDiseaseInfoByParentDiseaseId(String currentId);
}
