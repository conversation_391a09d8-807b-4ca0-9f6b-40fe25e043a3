package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcmrCustomizedSmsRuleMapper {
    int insert(List<TbCdcmrCustomizedSmsRule> record);


    int updateByPrimaryKeySelective(List<TbCdcmrCustomizedSmsRule> recordList);

    int deleteByCustomizedCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                                   @Param("loginUserId") String loginUserId, @Param("customizedCodeList") List<String> customizedCodeList);

    List<TbCdcmrCustomizedSmsRule> getListByLoginUserId(String loginUserId);

    List<TbCdcmrCustomizedSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                            @Param("loginUserId") String loginUserId);
}