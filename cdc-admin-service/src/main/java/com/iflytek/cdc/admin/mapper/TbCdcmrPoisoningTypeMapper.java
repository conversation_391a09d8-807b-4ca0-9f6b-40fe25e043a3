package com.iflytek.cdc.admin.mapper;


import com.iflytek.cdc.admin.entity.TbCdcmrPoisoningType;

import java.util.List;

public interface TbCdcmrPoisoningTypeMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrPoisoningType record);

    int insertSelective(TbCdcmrPoisoningType record);

    TbCdcmrPoisoningType selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrPoisoningType record);

    int updateByPrimaryKey(TbCdcmrPoisoningType record);

    List<TbCdcmrPoisoningType> getList();
}