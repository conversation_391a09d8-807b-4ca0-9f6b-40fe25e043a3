package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.DiseaseLevelPair;
import com.iflytek.cdc.admin.dto.EventChargePersonQuery;
import com.iflytek.cdc.admin.entity.TbCdcmrEventChargePerson;
import com.iflytek.cdc.admin.vo.EventChargePersonVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface TbCdcmrEventChargePersonMapper extends BaseMapper<TbCdcmrEventChargePerson> {
    TbCdcmrEventChargePerson loadById(@Param("id") String id);

    List<TbCdcmrEventChargePerson> bulkLoadByIds(@Param("ids") List<String> ids);

    void mergeInto(@Param("entities") List<TbCdcmrEventChargePerson> entities);

    List<EventChargePersonVO> listDiseaseConfig(EventChargePersonQuery query);

    List<EventChargePersonVO> getDealPersonInfoBy(@Param("dtoList") List<EventChargePersonQuery> dtoList);

    TbCdcmrEventChargePerson findByDiseaseCodeAndEventLevel(@Param("diseaseCode") String diseaseCode,@Param("eventLevelId") String eventLevelId);

    List<TbCdcmrEventChargePerson> findByDiseaseCodeLevelPairs(@Param("pairs") List<DiseaseLevelPair> pairs);
}