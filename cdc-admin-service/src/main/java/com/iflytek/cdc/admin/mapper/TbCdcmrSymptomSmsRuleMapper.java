package com.iflytek.cdc.admin.mapper;


import com.iflytek.cdc.admin.dto.TbCdcmrSymptomSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 症候群短信配置
 * */
public interface TbCdcmrSymptomSmsRuleMapper {


    int insert(List<TbCdcmrSymptomSmsRule> recordList);

    int updateByPrimaryKeySelective(List<TbCdcmrSymptomSmsRule> recordList);

    int deleteBySymptomCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                                @Param("loginUserId") String loginUserId, @Param("symptomCodeList") List<String> symptomCodeList);

    List<TbCdcmrSymptomSmsRule> getListByLoginUserId(String loginUserId);

    List<TbCdcmrSymptomSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                            @Param("loginUserId") String loginUserId);

}