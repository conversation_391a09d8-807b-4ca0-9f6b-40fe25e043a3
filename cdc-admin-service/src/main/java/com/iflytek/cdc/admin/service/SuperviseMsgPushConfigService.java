package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.SuperviseMsgPushConfigQueryDTO;
import com.iflytek.cdc.admin.entity.SuperviseMsgPushConfig;

public interface SuperviseMsgPushConfigService extends IService<SuperviseMsgPushConfig> {
    SuperviseMsgPushConfig create(SuperviseMsgPushConfig config, String loginUserId);

    SuperviseMsgPushConfig edit(SuperviseMsgPushConfig config, String loginUserId);

    Boolean delete(String id, String loginUserId);

    PageInfo<SuperviseMsgPushConfig> queryList(SuperviseMsgPushConfigQueryDTO dto, String loginUserId);

    SuperviseMsgPushConfig detail(String id);

    SuperviseMsgPushConfig queryByDiseaseClassCode(Integer diseaseClassCode,Integer msgType, String loginUserId);
}
