package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrStdTextConfig;

import java.util.List;

public interface TbCdcmrStdTextConfigMapper {

    TbCdcmrStdTextConfig selectByPrimaryKey(String id);

    List<TbCdcmrStdTextConfig> selectAll();

    int insert(TbCdcmrStdTextConfig record);

    int batchInsert(List<TbCdcmrStdTextConfig> list);

    int mergeInfo(List<TbCdcmrStdTextConfig> list);

}