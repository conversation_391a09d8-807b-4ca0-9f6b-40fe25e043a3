package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.ExportConfigDTO;
import com.iflytek.cdc.admin.dto.ExportConfigQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrExportPermissionConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TbCdcmrExportPermissionConfigMapper extends BaseMapper<TbCdcmrExportPermissionConfig> {

    List<ExportConfigDTO> queryExportConfigs(ExportConfigQueryDTO queryDTO);

    int updateApprovalRequired(@Param("id") String id, @Param("approvalRequired") boolean approvalRequired);

    @Select("SELECT * FROM tb_cdcmr_export_permission_config WHERE export_code = #{exportCode}")
    TbCdcmrExportPermissionConfig selectByExportCode(@Param("exportCode") String exportCode);

    List<String> selectNotExportConfig(@Param("moduleType") String moduleType, @Param("orgId") String orgId);

    ExportConfigDTO selectByExportCodeAndOrgId(@Param("exportCode") String exportCode, @Param("orgId") String orgId);
}