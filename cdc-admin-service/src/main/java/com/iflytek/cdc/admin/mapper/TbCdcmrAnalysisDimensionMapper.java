package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.AnalysisDimQueryDto;
import com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimension;
import com.iflytek.cdc.admin.vo.AnalysisDimensionVO;

import java.util.List;

public interface TbCdcmrAnalysisDimensionMapper extends BaseMapper<TbCdcmrAnalysisDimension> {

    /**
     * 刪除
     **/
    int softDelete(String id);

    List<AnalysisDimensionVO> pageList(AnalysisDimQueryDto queryDto);

    AnalysisDimensionVO selectVoById(String id);

    /**
     * 获取最大的序号
     */
    Integer loadLastNum();
}
