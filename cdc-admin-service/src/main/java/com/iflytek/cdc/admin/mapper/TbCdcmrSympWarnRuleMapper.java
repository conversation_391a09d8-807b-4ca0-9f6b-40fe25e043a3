package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.SympWarnRuleExportDataDto;
import com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrSympWarnRuleMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrSympWarnRule record);

    int insertSelective(TbCdcmrSympWarnRule record);

    TbCdcmrSympWarnRule selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrSympWarnRule record);

    int updateByPrimaryKey(TbCdcmrSympWarnRule record);

    int deleteOtherByIds(@Param("idList") List<String> idList, @Param("warnId") String warnId);

    int upsertRules(List<TbCdcmrSympWarnRule> recordList);

    List<TbCdcmrSympWarnRule> getRuleListByWarnId(String warnId);

    List<TbCdcmrSympWarnRule> getAllRuleList();

    List<SympWarnRuleExportDataDto> getExportData();

}