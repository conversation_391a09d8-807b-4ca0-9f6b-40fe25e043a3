package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.LexiconAssociation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LexiconAssociationMapper extends BaseMapper<LexiconAssociation> {

    boolean isDiseaseAssociated(@Param("leftLexiconId") String leftLexiconId,
                                @Param("rightLexiconId") String rightLexiconId,
                                @Param("associationType") String associationType);

    List<LexiconAssociation> findByLeftLexiconId(@Param("leftId") String leftId,@Param("associationType") String associationType);
}
