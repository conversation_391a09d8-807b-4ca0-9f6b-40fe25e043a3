package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.BizDiseasePermissionQueryDTO;
import com.iflytek.cdc.admin.dto.BizPermissionQueryDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityQueryDTO;
import com.iflytek.cdc.admin.entity.BusinessPermission;
import com.iflytek.cdc.admin.entity.DiseasePermission;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo;
import com.iflytek.cdc.admin.mapper.BizProcessConfigMapper;
import com.iflytek.cdc.admin.mapper.BusinessPermissionMapper;
import com.iflytek.cdc.admin.mapper.DiseasePermissionMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrSyndromeDiseaseInfoMapper;
import com.iflytek.cdc.admin.model.mr.vo.DeptUserPermissionVO;
import com.iflytek.cdc.admin.model.mr.vo.PermissionDiseaseInfoDataVO;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.model.processCheck.vo.CheckPersonInfo;
import com.iflytek.cdc.admin.service.BizProcessConfigService;
import com.iflytek.cdc.admin.service.BusinessProcessPermissionService;
import com.iflytek.cdc.admin.service.DiseasePermissionService;
import com.iflytek.cdc.admin.service.province.DimensionService;
import com.iflytek.cdc.admin.vo.BizProcessCheckAuthorityVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapOrganization;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgDto;
import com.iflytek.zhyl.uap.usercenter.pojo.cdc.DepartmentDto;
import com.iflytek.zhyl.uap.usercenter.service.UapOrgApi;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.enums.BizPermissionTypeEnum.*;
import static com.iflytek.cdc.admin.enums.BizSystemEnum.*;

@Service
public class BusinessProcessPermissionServiceImpl implements BusinessProcessPermissionService {

    private static final Map<String, List<String>> bizPermissionMap = new ConcurrentHashMap<>();

    static {
        bizPermissionMap.put(CDC_EDR_MANAGER.getCode(), Lists.newArrayList(ARCHIVE_APPROVAL.getCode(),DATA_EXPORT_APPROVAL.getCode()));
        bizPermissionMap.put(CDC_DISEASE_REPORT.getCode(), Lists.newArrayList(PROCESS_CHECK.getCode(),DATA_EXPORT_APPROVAL.getCode(),BRIEF_VIEW.getCode()));
        bizPermissionMap.put(CDC_SYNDROME_MONITOR.getCode(), Lists.newArrayList(PROCESS_CHECK.getCode(),DATA_EXPORT_APPROVAL.getCode(),BRIEF_VIEW.getCode()));
        bizPermissionMap.put(CDC_SITUATION_WARNING.getCode(), Lists.newArrayList(SIGNAL_PROCESS.getCode(),DATA_EXPORT_APPROVAL.getCode()));
        bizPermissionMap.put(CDC_SYNDROME_WARNING.getCode(), Lists.newArrayList(SIGNAL_PROCESS.getCode(),DATA_EXPORT_APPROVAL.getCode()));
        bizPermissionMap.put(CDC_EMERGENCY_MANAGE.getCode(), Lists.newArrayList(EVENT_PROCESS.getCode(),DATA_EXPORT_APPROVAL.getCode(),BRIEF_VIEW.getCode()));
    }



    private BusinessPermissionMapper businessPermissionMapper;

    @Autowired
    public void setBusinessPermissionMapper(BusinessPermissionMapper businessPermissionMapper) {
        this.businessPermissionMapper = businessPermissionMapper;
    }

    private DiseasePermissionMapper diseasePermissionMapper;

    @Autowired
    public void setDiseasePermissionMapper(DiseasePermissionMapper diseasePermissionMapper) {
        this.diseasePermissionMapper = diseasePermissionMapper;
    }

    private DiseasePermissionService diseasePermissionService;

    @Autowired
    public void setDiseasePermissionService(DiseasePermissionService diseasePermissionService) {
        this.diseasePermissionService = diseasePermissionService;
    }

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }


    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }


    private TbCdcmrInfectedDiseaseInfoMapper tbCdcmrInfectedDiseaseInfoMapper;

    @Autowired
    public void setTbCdcmrInfectedDiseaseInfoMapper(TbCdcmrInfectedDiseaseInfoMapper tbCdcmrInfectedDiseaseInfoMapper) {
        this.tbCdcmrInfectedDiseaseInfoMapper = tbCdcmrInfectedDiseaseInfoMapper;
    }


    private TbCdcmrSyndromeDiseaseInfoMapper tbCdcmrSyndromeDiseaseInfoMapper;

    @Autowired
    public void setTbCdcmrSyndromeDiseaseInfoMapper(TbCdcmrSyndromeDiseaseInfoMapper tbCdcmrSyndromeDiseaseInfoMapper) {
        this.tbCdcmrSyndromeDiseaseInfoMapper = tbCdcmrSyndromeDiseaseInfoMapper;
    }


    private DimensionService dimensionService;

    @Autowired
    public void setDimensionService(DimensionService dimensionService) {
        this.dimensionService = dimensionService;
    }

    private TbCdcmrInfectedDiseaseInfoMapper infectedDiseaseInfoMapper;

    @Autowired
    public void setInfectedDiseaseInfoMapper(TbCdcmrInfectedDiseaseInfoMapper infectedDiseaseInfoMapper) {
        this.infectedDiseaseInfoMapper = infectedDiseaseInfoMapper;
    }

    private UapOrgApi uapOrgApi;

    @Autowired
    public void setUapOrgApi(UapOrgApi uapOrgApi) {
        this.uapOrgApi = uapOrgApi;
    }


    private BizProcessConfigMapper bizProcessConfigMapper;

    @Autowired
    public void setBizProcessConfigMapper(BizProcessConfigMapper bizProcessConfigMapper) {
        this.bizProcessConfigMapper = bizProcessConfigMapper;
    }

    private BizProcessConfigService bizProcessConfigService;

    @Autowired
    public void setBizProcessConfigService(BizProcessConfigService bizProcessConfigService) {
        this.bizProcessConfigService = bizProcessConfigService;
    }

    @Override
    public BusinessPermission queryBizPermission(BizPermissionQueryDTO dto,String loginUserId) {
        String subSystemCode = dto.getSubSystemCode();
        String orgId = dto.getOrgId();
        if (StrUtil.isBlank(orgId)){
            UapUserPo user = uapServiceApi.getUser(loginUserId);
            orgId = user.getOrgId();
        }

        if (StrUtil.isBlank(subSystemCode)){
            throw new MedicalBusinessException("缺少参数");
        }
        BusinessPermission businessPermission = businessPermissionMapper.queryByOrgIdAndBizSystemCode(orgId,subSystemCode);
        if (businessPermission == null){
            // 进行初始化
            businessPermission = createBizPermission(orgId,subSystemCode,loginUserId);

        }
        return businessPermission;
    }

    private BusinessPermission createBizPermission(String orgId, String subSystemCode,String loginUserId) {

        UapUserPo user = uapServiceApi.getUser(loginUserId);

        BusinessPermission businessPermission = new BusinessPermission();
        businessPermission.setId(String.valueOf(batchUidService.getUid(BusinessPermission.TB_NAME)));
        businessPermission.setOrgId(orgId);
        businessPermission.setSubsystemCode(subSystemCode);
        businessPermission.setCreatorId(loginUserId);
        businessPermission.setCreator(user.getName());
        businessPermission.setCreateTime(new Date());
        businessPermission.setUpdaterId(loginUserId);
        businessPermission.setUpdater(user.getName());
        businessPermission.setUpdateTime(new Date());

        businessPermission.setBusinessTypeArr(bizPermissionMap.get(subSystemCode));
        businessPermissionMapper.insert(businessPermission);

        return businessPermission;
    }

    @Override
    public BusinessPermission updateBizConfig(BizPermissionQueryDTO dto, String loginUserId) {

        if (dto == null){
            throw new MedicalBusinessException("缺少参数");
        }
        String id = dto.getId();
        if (StrUtil.isBlank(id)){
            throw new MedicalBusinessException("缺少参数");
        }

        BusinessPermission businessPermission = businessPermissionMapper.selectById(id);
        if (businessPermission == null){
            throw new MedicalBusinessException("未查询到配置");
        }
        List<String> businessTypeArr = dto.getBusinessTypeArr();
        if (businessTypeArr == null){
            businessTypeArr = new ArrayList<>();
        }
        String subSystemCode = dto.getSubSystemCode();
        if (StrUtil.isBlank(subSystemCode)){
            throw new MedicalBusinessException("缺少参数");
        }
        if (!businessTypeArr.isEmpty()){
            List<String> bizPermissionList = bizPermissionMap.get(dto.getSubSystemCode());
            // 校验
            boolean flag = new HashSet<>(bizPermissionList).containsAll(businessTypeArr);
            if (!flag){
                throw new MedicalBusinessException("业务权限设置错误");
            }
        }

        businessPermission.setBusinessTypeArr(businessTypeArr);

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        businessPermission.setUpdaterId(loginUserId);
        businessPermission.setUpdater(user.getName());
        businessPermission.setUpdateTime(new Date());

        businessPermissionMapper.updateById(businessPermission);
        return businessPermission;
    }


    @Override
    public DiseasePermission queryDiseaseConfig(BizDiseasePermissionQueryDTO dto, String loginUserId) {

        String orgId = dto.getOrgId();
        if (StrUtil.isBlank(orgId)){
            UapUserPo user = uapServiceApi.getUser(loginUserId);
            orgId = user.getOrgId();
        }
        String subSystemCode = dto.getSubSystemCode();
        if (StrUtil.isBlank(subSystemCode)){
            throw new MedicalBusinessException("缺少参数");
        }

        // 先查询自己有没有
        DiseasePermission diseasePermission = diseasePermissionMapper.queryByOrgIdAndBizSystemCode(orgId,subSystemCode);

        if (diseasePermission == null){
            // 进行初始化
            diseasePermission = createDiseasePermission(orgId,subSystemCode,loginUserId);

        }
        return diseasePermission;
    }

    private DiseasePermission createDiseasePermission(String orgId, String subSystemCode,String loginUserId) {


        // 初始化三个
        List<DiseasePermission> list = Lists.newArrayList();
        DiseasePermission diseasePermissionEDR = buildDiseasePermission(orgId, CDC_EDR_MANAGER.getCode(), loginUserId);
        DiseasePermission diseasePermissionDisease = buildDiseasePermission(orgId, CDC_DISEASE_REPORT.getCode(), loginUserId);
        DiseasePermission diseasePermissionSyndrome = buildDiseasePermission(orgId, CDC_SYNDROME_MONITOR.getCode(), loginUserId);
        list.add(diseasePermissionEDR);
        list.add(diseasePermissionDisease);
        list.add(diseasePermissionSyndrome);

        diseasePermissionService.saveBatch(list);
        if (StrUtil.equals(subSystemCode,CDC_EDR_MANAGER.getCode())){
            return diseasePermissionEDR;
        }
        if (StrUtil.equals(subSystemCode,CDC_DISEASE_REPORT.getCode())){
            return diseasePermissionDisease;
        }
        if (StrUtil.equals(subSystemCode,CDC_SYNDROME_MONITOR.getCode())){
            return diseasePermissionSyndrome;
        }
        return null;
    }

    private DiseasePermission buildDiseasePermission(String orgId,String subSystemCode,String loginUserId) {

        UapUserPo user = uapServiceApi.getUser(loginUserId);

        DiseasePermission diseasePermissionEDR = new DiseasePermission();
        diseasePermissionEDR.setId(String.valueOf(batchUidService.getUid(DiseasePermission.TB_NAME)));
        diseasePermissionEDR.setOrgId(orgId);
        diseasePermissionEDR.setSubsystemCode(subSystemCode);
        // 初始化的时候直接设置为全部
        if (StrUtil.equals(subSystemCode,CDC_EDR_MANAGER.getCode())){
            diseasePermissionEDR.setInfectedAllFlag("0");
            diseasePermissionEDR.setSyndromeAllFlag("0");
        }
        if (StrUtil.equals(subSystemCode,CDC_DISEASE_REPORT.getCode())){
            diseasePermissionEDR.setInfectedAllFlag("0");
        }
        if (StrUtil.equals(subSystemCode,CDC_SYNDROME_MONITOR.getCode())){
            diseasePermissionEDR.setSyndromeAllFlag("0");
        }
        diseasePermissionEDR.setInfectedDiseaseArr(null);
        diseasePermissionEDR.setSyndromeDiseaseArr(null);

        diseasePermissionEDR.setCreateTime(new Date());
        diseasePermissionEDR.setUpdateTime(new Date());
        diseasePermissionEDR.setCreatorId(loginUserId);
        diseasePermissionEDR.setCreator(user.getName());
        diseasePermissionEDR.setUpdaterId(loginUserId);
        diseasePermissionEDR.setUpdater(user.getName());

        return diseasePermissionEDR;
    }

    @Override
    public DiseasePermission updateDiseaseConfig(BizDiseasePermissionQueryDTO dto, String loginUserId) {
        if (dto == null){
            throw new MedicalBusinessException("缺少参数");
        }
        String id = dto.getId();
        if (StrUtil.isBlank(id)){
            throw new MedicalBusinessException("缺少参数");
        }
        String subSystemCode = dto.getSubSystemCode();
        if (StrUtil.isBlank(subSystemCode)){
            throw new MedicalBusinessException("缺少参数");
        }

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        if (StrUtil.equals(subSystemCode,CDC_EDR_MANAGER.getCode())){
            DiseasePermission diseasePermission = diseasePermissionService.getById(id);
            if (diseasePermission == null){
                throw new MedicalBusinessException("未查询到配置");
            }
            String infectedAllFlag = dto.getInfectedAllFlag();
            if (StrUtil.isBlank(infectedAllFlag)){
                throw new MedicalBusinessException("缺少参数");
            }
            if (StrUtil.equals(infectedAllFlag,"0")){
                diseasePermission.setInfectedDiseaseArr(null);
                diseasePermission.setInfectedAllFlag("0");
            } else {
                diseasePermission.setInfectedDiseaseArr(dto.getInfectedDiseaseArr());
                diseasePermission.setInfectedAllFlag("1");
                diseasePermission.setInfectedDiseaseInfo(dto.getInfectedDiseaseInfo());
            }
            String syndromeAllFlag = dto.getSyndromeAllFlag();
            if (StrUtil.isBlank(syndromeAllFlag)){
                throw new MedicalBusinessException("缺少参数");
            }
            if (StrUtil.equals(syndromeAllFlag,"0")){
                diseasePermission.setSyndromeDiseaseArr(null);
                diseasePermission.setSyndromeAllFlag("0");
            } else {
                diseasePermission.setSyndromeDiseaseArr(dto.getSyndromeDiseaseArr());
                diseasePermission.setSyndromeAllFlag("1");
                diseasePermission.setSyndromeDiseaseInfo(dto.getSyndromeDiseaseInfo());
            }


            diseasePermission.setUpdaterId(loginUserId);
            diseasePermission.setUpdater(user.getName());
            diseasePermission.setUpdateTime(new Date());

            diseasePermissionService.updateById(diseasePermission);

            // 同步修改
            LambdaQueryWrapper<DiseasePermission> infectedWrapper = new LambdaQueryWrapper<>();
            infectedWrapper.eq(DiseasePermission::getOrgId,diseasePermission.getOrgId())
                    .eq(DiseasePermission::getSubsystemCode,CDC_DISEASE_REPORT.getCode());
            DiseasePermission diseasePermissionDisease = diseasePermissionService.getOne(infectedWrapper);
            if (StrUtil.equals(infectedAllFlag,"0")){
                diseasePermissionDisease.setInfectedDiseaseArr(null);
                diseasePermissionDisease.setInfectedAllFlag("0");
            } else {
                diseasePermissionDisease.setInfectedDiseaseArr(dto.getInfectedDiseaseArr());
                diseasePermissionDisease.setInfectedAllFlag("1");
                diseasePermissionDisease.setInfectedDiseaseInfo(dto.getInfectedDiseaseInfo());
            }
            diseasePermissionDisease.setUpdaterId(loginUserId);
            diseasePermissionDisease.setUpdater(user.getName());
            diseasePermissionDisease.setUpdateTime(new Date());
            diseasePermissionService.updateById(diseasePermissionDisease);
            updateBizProcessCheckAuthority(diseasePermissionDisease);

            LambdaQueryWrapper<DiseasePermission> syndromeWrapper = new LambdaQueryWrapper<>();
            syndromeWrapper.eq(DiseasePermission::getOrgId,diseasePermission.getOrgId())
                    .eq(DiseasePermission::getSubsystemCode,CDC_SYNDROME_MONITOR.getCode());
            DiseasePermission diseasePermissionSyndrome = diseasePermissionService.getOne(syndromeWrapper);
            if (StrUtil.equals(syndromeAllFlag,"0")){
                diseasePermissionSyndrome.setSyndromeDiseaseArr(null);
                diseasePermissionSyndrome.setSyndromeAllFlag("0");
            } else {
                diseasePermissionSyndrome.setSyndromeDiseaseArr(dto.getSyndromeDiseaseArr());
                diseasePermissionSyndrome.setSyndromeAllFlag("1");
                diseasePermissionSyndrome.setSyndromeDiseaseInfo(dto.getSyndromeDiseaseInfo());
            }
            diseasePermissionSyndrome.setUpdaterId(loginUserId);
            diseasePermissionSyndrome.setUpdater(user.getName());
            diseasePermissionSyndrome.setUpdateTime(new Date());
            diseasePermissionService.updateById(diseasePermissionSyndrome);
            updateBizProcessCheckAuthority(diseasePermissionSyndrome);

            return diseasePermission;

        }

        if (StrUtil.equals(subSystemCode,CDC_DISEASE_REPORT.getCode())){
            DiseasePermission diseasePermission = diseasePermissionService.getById(id);
            if (diseasePermission == null){
                throw new MedicalBusinessException("未查询到配置");
            }
            String infectedAllFlag = dto.getInfectedAllFlag();
            if (StrUtil.isBlank(infectedAllFlag)){
                throw new MedicalBusinessException("缺少参数");
            }
            if (StrUtil.equals(infectedAllFlag,"0")){
                diseasePermission.setInfectedDiseaseArr(null);
                diseasePermission.setInfectedAllFlag("0");
            } else {
                diseasePermission.setInfectedDiseaseArr(dto.getInfectedDiseaseArr());
                diseasePermission.setInfectedAllFlag("1");
                diseasePermission.setInfectedDiseaseInfo(dto.getInfectedDiseaseInfo());
            }
            diseasePermission.setUpdaterId(loginUserId);
            diseasePermission.setUpdater(user.getName());
            diseasePermission.setUpdateTime(new Date());
            diseasePermissionService.updateById(diseasePermission);
            updateBizProcessCheckAuthority(diseasePermission);

            // 修改edr的数据
            LambdaQueryWrapper<DiseasePermission> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DiseasePermission::getOrgId,diseasePermission.getOrgId())
                    .eq(DiseasePermission::getSubsystemCode,CDC_EDR_MANAGER.getCode());
            DiseasePermission diseasePermissionEDR = diseasePermissionService.getOne(wrapper);
            if (StrUtil.equals(infectedAllFlag,"0")){
                diseasePermissionEDR.setInfectedDiseaseArr(null);
                diseasePermissionEDR.setInfectedAllFlag("0");
            } else {
                diseasePermissionEDR.setInfectedDiseaseArr(dto.getInfectedDiseaseArr());
                diseasePermissionEDR.setInfectedAllFlag("1");
                diseasePermissionEDR.setInfectedDiseaseInfo(dto.getInfectedDiseaseInfo());
            }
            diseasePermissionEDR.setUpdaterId(loginUserId);
            diseasePermissionEDR.setUpdater(user.getName());
            diseasePermissionEDR.setUpdateTime(new Date());
            diseasePermissionService.updateById(diseasePermissionEDR);

            return diseasePermission;
        }

        if (StrUtil.equals(subSystemCode,CDC_SYNDROME_MONITOR.getCode())){
            DiseasePermission diseasePermission = diseasePermissionService.getById(id);
            if (diseasePermission == null){
                throw new MedicalBusinessException("未查询到配置");
            }

            String syndromeAllFlag = dto.getSyndromeAllFlag();
            if (StrUtil.isBlank(syndromeAllFlag)){
                throw new MedicalBusinessException("缺少参数");
            }
            if (StrUtil.equals(syndromeAllFlag,"0")){
                diseasePermission.setSyndromeDiseaseArr(null);
                diseasePermission.setSyndromeAllFlag("0");
            } else {
                diseasePermission.setSyndromeDiseaseArr(dto.getSyndromeDiseaseArr());
                diseasePermission.setSyndromeAllFlag("1");
                diseasePermission.setSyndromeDiseaseInfo(dto.getSyndromeDiseaseInfo());
            }
            diseasePermission.setUpdaterId(loginUserId);
            diseasePermission.setUpdater(user.getName());
            diseasePermission.setUpdateTime(new Date());
            diseasePermissionService.updateById(diseasePermission);
            updateBizProcessCheckAuthority(diseasePermission);

            // 修改edr的数据
            LambdaQueryWrapper<DiseasePermission> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DiseasePermission::getOrgId,diseasePermission.getOrgId())
                    .eq(DiseasePermission::getSubsystemCode,CDC_EDR_MANAGER.getCode());
            DiseasePermission diseasePermissionEDR = diseasePermissionService.getOne(wrapper);
            if (StrUtil.equals(syndromeAllFlag,"0")){
                diseasePermissionEDR.setSyndromeDiseaseArr(null);
                diseasePermissionEDR.setSyndromeAllFlag("0");
            } else {
                diseasePermissionEDR.setSyndromeDiseaseArr(dto.getSyndromeDiseaseArr());
                diseasePermissionEDR.setSyndromeAllFlag("1");
                diseasePermissionEDR.setSyndromeDiseaseInfo(dto.getSyndromeDiseaseInfo());
            }
            diseasePermissionEDR.setUpdaterId(loginUserId);
            diseasePermissionEDR.setUpdater(user.getName());
            diseasePermissionEDR.setUpdateTime(new Date());
            diseasePermissionService.updateById(diseasePermissionEDR);

            return diseasePermission;
        }

        return null;
    }

    @Override
    public DeptUserPermissionVO queryDeptUserPermission(String subSystemCode,String loginUserId) {
        DeptUserPermissionVO vo = new DeptUserPermissionVO();
        vo.setDeptFlag(false);
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        String orgId = user.getOrgId();
        DepartmentDto departmentDto = uapServiceApi.queryDeptNames(orgId, loginUserId);
        if (departmentDto == null){
            return vo;
        }
        String departmentCode = departmentDto.getDepartmentCode();
        if (StrUtil.isBlank(departmentCode)){
            return vo;
        }
        // 走到这里说明用户在部门里面 部门才设置疾病权限
        // 查询疾病权限
        BizDiseasePermissionQueryDTO dto = new BizDiseasePermissionQueryDTO();
        dto.setOrgId(departmentCode);
        dto.setSubSystemCode(subSystemCode);
        DiseasePermission diseasePermission = queryDiseaseConfig(dto, loginUserId);
        vo.setDiseasePermission(diseasePermission);
        vo.setDeptFlag(true);
        return vo;
    }

    @Override
    public PermissionDiseaseInfoDataVO queryPermissionDiseaseInfoData(String subSystemCode, String loginUserId) {

        PermissionDiseaseInfoDataVO permissionDiseaseInfoDataVO = new PermissionDiseaseInfoDataVO();
        permissionDiseaseInfoDataVO.setDeptFlag(false);
        DeptUserPermissionVO deptUserPermissionVO = queryDeptUserPermission(subSystemCode, loginUserId);
        Boolean deptFlag = deptUserPermissionVO.getDeptFlag();
        // 如果不是部门用户
        if (!deptFlag){

            return permissionDiseaseInfoDataVO;
        }
        permissionDiseaseInfoDataVO.setDeptFlag(true);
        DiseasePermission diseasePermission = deptUserPermissionVO.getDiseasePermission();
        if (StrUtil.equals(subSystemCode,CDC_DISEASE_REPORT.getCode())){
            String infectedAllFlag = diseasePermission.getInfectedAllFlag();
            permissionDiseaseInfoDataVO.setInfectedAllFlag(infectedAllFlag);
            if (StrUtil.equals(infectedAllFlag,"0")){
                // 表示全部传染病 直接返回
                return permissionDiseaseInfoDataVO;
            } else {
                // 查询传染病最终的集合
                List<DiseasePermission.DiseaseInfoData> infectedDiseaseArr = diseasePermission.getInfectedDiseaseArr();
                List<String> infectedDiseaseList = buildInfectedDiseaseList(infectedDiseaseArr);
                permissionDiseaseInfoDataVO.setCodeList(infectedDiseaseList);

            }
        }
        if (StrUtil.equals(subSystemCode,CDC_SYNDROME_MONITOR.getCode())){
            String syndromeAllFlag = diseasePermission.getSyndromeAllFlag();
            permissionDiseaseInfoDataVO.setSyndromeAllFlag(syndromeAllFlag);
            if (StrUtil.equals(syndromeAllFlag,"0")){
                // 表示全部传染病 直接返回
                return permissionDiseaseInfoDataVO;
            } else {
                // 查询症候群最终集合
                List<DiseasePermission.DiseaseInfoData> syndromeDiseaseArr = diseasePermission.getSyndromeDiseaseArr();
                List<String> syndromeDiseaseList = buildSyndromeDiseaseList(syndromeDiseaseArr);
                permissionDiseaseInfoDataVO.setCodeList(syndromeDiseaseList);
            }
        }


        return permissionDiseaseInfoDataVO;
    }

    private List<String> buildSyndromeDiseaseList(List<DiseasePermission.DiseaseInfoData> syndromeDiseaseArr) {
        if (syndromeDiseaseArr.isEmpty()){
            return new ArrayList<>();
        }
        List<String> collect = syndromeDiseaseArr.stream().map(DiseasePermission.DiseaseInfoData::getDiseaseCode).collect(Collectors.toList());
        Set<String> codeList = new HashSet<>();
        for (String item : collect) {
            SyndromeDiseaseInfoVO syndromeDiseaseInfoVO = tbCdcmrSyndromeDiseaseInfoMapper.loadByCode(item);
            if (syndromeDiseaseInfoVO != null){
                // 使用队列存储待处理的疾病ID，实现广度优先遍历
                Queue<SyndromeDiseaseInfoVO> diseaseQueue = new LinkedList<>();
                diseaseQueue.offer(syndromeDiseaseInfoVO);

                // 使用 while 循环处理队列中的每个疾病ID
                while (!diseaseQueue.isEmpty()) {
                    SyndromeDiseaseInfoVO currentSyndromeDiseaseInfoVO = diseaseQueue.poll();
                    codeList.add(currentSyndromeDiseaseInfoVO.getDiseaseCode());
                    // 查询当前ID的子类疾病
                    List<SyndromeDiseaseInfoVO> childDiseaseList = tbCdcmrSyndromeDiseaseInfoMapper.getSyndromeDiseaseInfoByParentDiseaseId(currentSyndromeDiseaseInfoVO.getId());
                    if (!childDiseaseList.isEmpty()) {
                        for (SyndromeDiseaseInfoVO childDisease : childDiseaseList) {
                            diseaseQueue.offer(childDisease);
                        }

                    }
                }
            }
        }
        if (codeList.isEmpty()){
            return new ArrayList<>();
        }
        return new ArrayList<>(codeList);
    }

    private List<String> buildInfectedDiseaseList(List<DiseasePermission.DiseaseInfoData> infectedDiseaseArr) {

        if (infectedDiseaseArr.isEmpty()){
            return new ArrayList<>();
        }
        // 先获取 code
        List<String> collect = new ArrayList<>();
        for (DiseasePermission.DiseaseInfoData diseaseInfoData : infectedDiseaseArr) {
            collect.add(diseaseInfoData.getDiseaseCode());
        }
        Set<String> codeList = new HashSet<>();

        // 使用 for 循环处理每个疾病代码
        for (String item : collect) {
            if (StrUtil.length(item) < 2){
                // 大类 根据 disease_type_code 获取
                List<TbCdcmrInfectedDiseaseInfo> infectedDiseaseInfoList = tbCdcmrInfectedDiseaseInfoMapper.getAllInfectedInfoBy(null, item);
                for (TbCdcmrInfectedDiseaseInfo infectedDiseaseInfo : infectedDiseaseInfoList) {
                    codeList.add(infectedDiseaseInfo.getId());
                }
            } else {
                // 子类 - 使用 while 循环递归查询所有子类
                TbCdcmrInfectedDiseaseInfo infectedDiseaseInfo = tbCdcmrInfectedDiseaseInfoMapper.getDiseaseInfoById(item);
                if (infectedDiseaseInfo != null) {
                    // 使用队列存储待处理的疾病ID，实现广度优先遍历
                    Queue<String> diseaseQueue = new LinkedList<>();
                    diseaseQueue.offer(infectedDiseaseInfo.getId());

                    // 使用 while 循环处理队列中的每个疾病ID
                    while (!diseaseQueue.isEmpty()) {
                        String currentId = diseaseQueue.poll();
                        codeList.add(currentId);
                        // 查询当前ID的子类疾病
                        List<TbCdcmrInfectedDiseaseInfo> childDiseaseList = tbCdcmrInfectedDiseaseInfoMapper.getDiseaseInfoByParentDiseaseId(currentId);

                        if (!childDiseaseList.isEmpty()) {
                            for (TbCdcmrInfectedDiseaseInfo childDisease : childDiseaseList) {
                                diseaseQueue.offer(childDisease.getId());
                            }
                        }
                    }
                }
            }
        }
        if (codeList.isEmpty()){
            return new ArrayList<>();
        }
        return new ArrayList<>(codeList);

    }

    @Override
    public List<TreeNode> queryPermissionInfectedInfoData(String loginUserId) {
        PermissionDiseaseInfoDataVO permissionDiseaseInfoDataVO = this.queryPermissionDiseaseInfoData(CDC_DISEASE_REPORT.getCode(), loginUserId);
        Boolean deptFlag = permissionDiseaseInfoDataVO.getDeptFlag();
        if (!deptFlag){
            // 不是部门用户
            List<TbCdcmrInfectedDiseaseInfo> allInfectedInfoBy = infectedDiseaseInfoMapper.getAllInfectedInfoBy(null, null);
            List<TreeNode> treeNodeList = buildInfectedDiseaseInfoTreeNode(allInfectedInfoBy);
            upgradeTreeNode(treeNodeList);
            return treeNodeList;
        }
        if (StrUtil.equals(permissionDiseaseInfoDataVO.getInfectedAllFlag(),"0")){
            // 全部传染病
            List<TbCdcmrInfectedDiseaseInfo> allInfectedInfoBy = infectedDiseaseInfoMapper.getAllInfectedInfoBy(null, null);
            List<TreeNode> treeNodeList = buildInfectedDiseaseInfoTreeNode(allInfectedInfoBy);
            upgradeTreeNode(treeNodeList);
            return treeNodeList;
        }
        List<String> codeList = permissionDiseaseInfoDataVO.getCodeList();
        if (codeList.isEmpty()){
            return Collections.emptyList();
        }
        // 倒查根据codeList查询出数据 然后进行构造树
        Set<TbCdcmrInfectedDiseaseInfo> infectedInfoByCodeSet = new HashSet<>();
        for (String item : codeList) {
            TbCdcmrInfectedDiseaseInfo diseaseInfoById = tbCdcmrInfectedDiseaseInfoMapper.getDiseaseInfoById(item);
            if (diseaseInfoById != null){
                // 使用队列存储待处理的疾病ID，实现广度优先遍历
                Queue<TbCdcmrInfectedDiseaseInfo> diseaseQueue = new LinkedList<>();
                diseaseQueue.offer(diseaseInfoById);
                while (!diseaseQueue.isEmpty()){
                    TbCdcmrInfectedDiseaseInfo currentInfectedDiseaseInfo = diseaseQueue.poll();
                    infectedInfoByCodeSet.add(currentInfectedDiseaseInfo);
                    TbCdcmrInfectedDiseaseInfo parentInfectedDiseaseInfo = tbCdcmrInfectedDiseaseInfoMapper.getDiseaseInfoById(currentInfectedDiseaseInfo.getParentDiseaseId());
                    if (parentInfectedDiseaseInfo != null){
                        diseaseQueue.offer(parentInfectedDiseaseInfo);
                    }

                }
            }
        }
        if (infectedInfoByCodeSet.isEmpty()){
            return Collections.emptyList();
        }
        List<TreeNode> treeNodes = buildInfectedDiseaseInfoTreeNode(new ArrayList<>(infectedInfoByCodeSet));
        upgradeTreeNode(treeNodes,codeList);
        return treeNodes;
    }

    private List<TreeNode> buildInfectedDiseaseInfoTreeNode(List<TbCdcmrInfectedDiseaseInfo> infectedInfoByCodeList) {
        List<TreeNode> secondNode = TreeNode.buildTreeByNodeList(infectedInfoByCodeList,
                TbCdcmrInfectedDiseaseInfo::getId,
                TbCdcmrInfectedDiseaseInfo::getParentDiseaseId,
                TbCdcmrInfectedDiseaseInfo::getDiseaseName,
                TbCdcmrInfectedDiseaseInfo::getId);
        Map<Pair<String, String>, List<TbCdcmrInfectedDiseaseInfo>> infectedClassMap = infectedInfoByCodeList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getDiseaseTypeCode()) && StringUtils.isNotBlank(e.getDiseaseTypeName()))
                .collect(Collectors.groupingBy(TbCdcmrInfectedDiseaseInfo::getDiseaseClass));
        List<TreeNode> rootNode = new ArrayList<>();
        for(Map.Entry<Pair<String, String>, List<TbCdcmrInfectedDiseaseInfo>> entry : infectedClassMap.entrySet()){

            //该病种类型的code-value
            Pair<String, String> diseaseClassPair = entry.getKey();
            //该病种下的疾病
            List<TbCdcmrInfectedDiseaseInfo> diseaseWarnings = entry.getValue();
            TreeNode root = TreeNode.from(diseaseClassPair.getFirst(), diseaseClassPair.getSecond(), diseaseClassPair.getFirst());
            Set<TreeNode> childNodes = new HashSet<>();
            //用第二层的疾病 匹配该病种下的疾病
            secondNode.forEach(e -> {
                for (TbCdcmrInfectedDiseaseInfo disease : diseaseWarnings) {
                    if(Objects.equals(e.getValue(), disease.getId()) || Objects.equals(e.getValue(), disease.getParentDiseaseId())){
                        childNodes.add(e);
                    }
                }
            });
            root.setChildren(new ArrayList<>(childNodes));
            rootNode.add(root);
        }

        //将所有叶子节点的children设置为null
        TreeNode.setChildrenToNull(rootNode);

        rootNode = rootNode.stream().sorted(Comparator.nullsLast(Comparator.comparing(TreeNode::getId))).collect(Collectors.toList());
        return rootNode;
    }

    @Override
    public List<SyndromeDiseaseInfoVO> queryPermissionSyndromeInfoData(String loginUserId) {
        PermissionDiseaseInfoDataVO permissionDiseaseInfoDataVO = this.queryPermissionDiseaseInfoData(CDC_SYNDROME_MONITOR.getCode(), loginUserId);
        Boolean deptFlag = permissionDiseaseInfoDataVO.getDeptFlag();
        if (!deptFlag){
            return tbCdcmrSyndromeDiseaseInfoMapper.getSyndromeTreeInfo();
        }
        if (StrUtil.equals(permissionDiseaseInfoDataVO.getInfectedAllFlag(),"0")){
            return tbCdcmrSyndromeDiseaseInfoMapper.getSyndromeTreeInfo();
        }
        List<String> codeList = permissionDiseaseInfoDataVO.getCodeList();
        if (codeList.isEmpty()){
            return Collections.emptyList();
        }
        // 倒查
        Set<SyndromeDiseaseInfoVO> syndromeInfoByCodeSet = new HashSet<>();
        for (String item : codeList) {
            SyndromeDiseaseInfoVO diseaseInfoById = tbCdcmrSyndromeDiseaseInfoMapper.loadByCode(item);
            if (diseaseInfoById != null){
                // 使用队列存储待处理的疾病ID，实现广度优先遍历
                Queue<SyndromeDiseaseInfoVO> diseaseQueue = new LinkedList<>();
                diseaseQueue.offer(diseaseInfoById);
                while (!diseaseQueue.isEmpty()){
                    SyndromeDiseaseInfoVO currentSyndromeDiseaseInfo = diseaseQueue.poll();
                    syndromeInfoByCodeSet.add(currentSyndromeDiseaseInfo);
                    SyndromeDiseaseInfoVO parentSyndromeDiseaseInfo = tbCdcmrSyndromeDiseaseInfoMapper.loadById(currentSyndromeDiseaseInfo.getDiseaseParentId());
                    if (parentSyndromeDiseaseInfo != null){
                        diseaseQueue.offer(parentSyndromeDiseaseInfo);
                    }

                }
            }
        }
        if (syndromeInfoByCodeSet.isEmpty()){
            return Collections.emptyList();
        }
        Set<String> codeSet = new HashSet<>(codeList);
        syndromeInfoByCodeSet.forEach(item -> {
            String diseaseCode = item.getDiseaseCode();
            if (codeSet.contains(diseaseCode)){
                item.setSelectEnable("1");
            }
        });
        return new ArrayList<>(syndromeInfoByCodeSet);
    }

    public void upgradeTreeNode(List<TreeNode> treeNodeList){
        upgradeTreeNode(treeNodeList,null);
    }

    public void upgradeTreeNode(List<TreeNode> treeNodeList, List<String> codeList) {
        if(codeList == null){
            // 遍历treeNodeList 针对全部节点设置
            for (TreeNode treeNode : treeNodeList) {
                setSelectEnableForAllNodes(treeNode);
            }
        } else {
            for (TreeNode treeNode : treeNodeList) {
                setSelectEnableForAllNodes(treeNode,new HashSet<>(codeList));
            }
            // 需要再次遍历看主节点是否可选
            for (TreeNode treeNode : treeNodeList) {
                updateParentSelectEnable(treeNode);
            }
        }
    }

    /**
     * 递归遍历树形结构，为每个节点设置selectEnable属性
     * @param treeNode 当前节点
     */
    private void setSelectEnableForAllNodes(TreeNode treeNode) {
        if (treeNode == null) {
            return;
        }

        // 为当前节点设置属性
        treeNode.setSelectEnable("1");

        // 递归处理所有子节点
        List<TreeNode> children = treeNode.getChildren();
        if (children != null && !children.isEmpty()) {
            for (TreeNode child : children) {
                setSelectEnableForAllNodes(child);
            }
        }
    }

    private void setSelectEnableForAllNodes(TreeNode treeNode,Set<String> codeSet) {
        if (treeNode == null) {
            return;
        }
        String id = treeNode.getId();
        if (codeSet.contains(id)){
            // 为当前节点设置属性
            treeNode.setSelectEnable("1");
        }

        // 递归处理所有子节点
        List<TreeNode> children = treeNode.getChildren();
        if (children != null && !children.isEmpty()) {
            for (TreeNode child : children) {
                setSelectEnableForAllNodes(child,codeSet);
            }
        }
    }

    /**
     * 递归更新父节点的selectEnable属性
     * 当所有子节点的selectEnable都为"1"时，将父节点也设置为"1"
     * @param treeNode 当前节点
     * @return 当前节点是否可选择（selectEnable为"1"）
     */
    private boolean updateParentSelectEnable(TreeNode treeNode) {
        if (treeNode == null) {
            return false;
        }

        List<TreeNode> children = treeNode.getChildren();

        // 如果是叶子节点，直接返回当前节点的selectEnable状态
        if (children == null || children.isEmpty()) {
            return "1".equals(treeNode.getSelectEnable());
        }

        // 递归检查所有子节点
        boolean allChildrenSelectable = true;
        for (TreeNode child : children) {
            boolean childSelectable = updateParentSelectEnable(child);
            if (!childSelectable) {
                allChildrenSelectable = false;
            }
        }

        // 如果所有子节点都可选择，则将当前节点设置为可选择
        if (allChildrenSelectable) {
            treeNode.setSelectEnable("1");
            return true;
        }

        // 如果不是所有子节点都可选择，返回当前节点的原始状态
        return "1".equals(treeNode.getSelectEnable());
    }

    // 当传染病和症候群的权限修改时
    public void updateBizProcessCheckAuthority(DiseasePermission diseasePermissionDisease) {
        String subsystemCode = diseasePermissionDisease.getSubsystemCode();

        String orgId = diseasePermissionDisease.getOrgId();
        // 找一下上级id
        UapOrgDto ext = uapOrgApi.getExt(orgId);
        if (ext == null){
            return;
        }
        TUapOrganization uapOrganization = ext.getUapOrganization();
        String higherOrgId = uapOrganization.getHigherOrg();
        if (StrUtil.isBlank(higherOrgId)){
            return;
        }
        String diseaseAllFlag = null;
        if (StrUtil.equals(subsystemCode,CDC_DISEASE_REPORT.getCode())){
            diseaseAllFlag = diseasePermissionDisease.getInfectedAllFlag();
        } else {
            diseaseAllFlag = diseasePermissionDisease.getSyndromeAllFlag();
        }
        // 修改为全部时不处理
        if (StrUtil.equals(diseaseAllFlag,"0")){
            return;
        }
        List<DiseasePermission.DiseaseInfoData> diseaseArr = null;
        if (StrUtil.equals(subsystemCode,CDC_DISEASE_REPORT.getCode())){
            diseaseArr = diseasePermissionDisease.getInfectedDiseaseArr();
        } else {
            diseaseArr = diseasePermissionDisease.getSyndromeDiseaseArr();
        }
        if (diseaseArr == null){
            return;
        }
        // 有权限的传染病code
        Set<String> codeSet = diseaseArr.stream().map(DiseasePermission.DiseaseInfoData::getDiseaseCode).collect(Collectors.toSet());

        // 查询出父级的病例审核配置
        BizProcessCheckAuthorityQueryDTO bizProcessCheckAuthorityQueryDTO = new BizProcessCheckAuthorityQueryDTO();
        bizProcessCheckAuthorityQueryDTO.setOrgId(higherOrgId);
        bizProcessCheckAuthorityQueryDTO.setSubSystemCode(subsystemCode);
        List<BizProcessCheckAuthorityVO> bizProcessCheckAuthorityVOList = bizProcessConfigMapper.queryList(bizProcessCheckAuthorityQueryDTO);

        // 没有疾病权限的配置
        List<BizProcessCheckAuthorityVO> noPermissionList = bizProcessCheckAuthorityVOList.stream().filter(item -> {
            String diseaseCode = item.getDiseaseCode();
            return !codeSet.contains(diseaseCode);
        }).collect(Collectors.toList());
        if (noPermissionList.isEmpty()){
            return;
        }
        noPermissionList.forEach(item -> {
            String checkPerson = item.getCheckPerson();
            if (StrUtil.isNotBlank(checkPerson)){
                List<CheckPersonInfo> list = JSONUtil.toList(checkPerson, CheckPersonInfo.class);
                for (CheckPersonInfo checkPersonInfo : list) {
                    String orgId1 = checkPersonInfo.getOrgId();
                    if (StrUtil.isNotBlank(orgId1)){
                        if (StrUtil.equals(orgId1,orgId)){
                            list.remove(checkPersonInfo);
                        }
                    }
                }
                item.setCheckPerson(JSONUtil.toJsonStr(list));
            }
        });
        // 进行更新
        bizProcessConfigMapper.updateCheckPerson(noPermissionList);
    }
}
