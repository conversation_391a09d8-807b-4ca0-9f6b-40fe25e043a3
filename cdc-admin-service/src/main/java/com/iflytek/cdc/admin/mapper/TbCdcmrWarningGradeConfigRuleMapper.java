package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrWarningGradeConfigRuleMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrWarningGradeConfigRule record);

    int insertSelective(TbCdcmrWarningGradeConfigRule record);

    TbCdcmrWarningGradeConfigRule selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrWarningGradeConfigRule record);

    int updateByPrimaryKey(TbCdcmrWarningGradeConfigRule record);

    List<TbCdcmrWarningGradeConfigRule> getByConfigId(String configId);

    List<TbCdcmrWarningGradeConfigRule> getAllByConfigId(String configId);


    int deleteOtherByIds(@Param("idList") List<String> idList, @Param("configId") String configId);

    int deleteByGradeCode(@Param("gradeCode") String gradeCode);


    int upsertRules(List<TbCdcmrWarningGradeConfigRule> recordList);
}