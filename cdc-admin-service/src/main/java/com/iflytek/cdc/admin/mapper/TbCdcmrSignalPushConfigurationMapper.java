package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfiguration;
import com.iflytek.cdc.admin.vo.SignalPushConfigurationVO;

import java.util.List;

public interface TbCdcmrSignalPushConfigurationMapper extends BaseMapper<TbCdcmrSignalPushConfiguration> {
    void batchInsert(List<TbCdcmrSignalPushConfiguration> insertList);

    void batchUpdate(List<TbCdcmrSignalPushConfiguration> updateList);

    SignalPushConfigurationVO loadSignPushConfigByDiseaseCode(String topicId);

    List<TbCdcmrSignalPushConfiguration> selectByDiseaceCodeAndRiskLevelDetailId(List<SignalPushRuleDTO> input);

    TbCdcmrSignalPushConfiguration selectByDiseaseCodeAndRiskLevelDetailId(SignalPushRuleDTO input);

    TbCdcmrSignalPushConfiguration selectByWarningChargePersonTableId(String id);

    List<TbCdcmrSignalPushConfiguration> selectAll();
}