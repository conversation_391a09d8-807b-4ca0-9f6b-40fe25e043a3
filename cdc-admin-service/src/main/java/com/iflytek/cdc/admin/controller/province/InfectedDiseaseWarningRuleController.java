package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.InfectedWarningRuleConstants;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseWarningRule;
import com.iflytek.cdc.admin.model.mr.dto.InfectedWarningRuleQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.InfectedProcessScopeVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.InfectedDiseaseWarningRuleService;
import com.iflytek.cdc.admin.service.province.InfectedDiseaseWarningService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 传染病预警-传染病预警规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@RestController
@Api(tags = "预警规则维护-传染病")
@RequestMapping("/pt/{version}/infectedDiseaseWarning")
public class InfectedDiseaseWarningRuleController {

    @Resource
    private InfectedDiseaseWarningRuleService infectedDiseaseWarningRuleService;

    @Resource
    private InfectedDiseaseWarningService infectedDiseaseWarningService;


    @PostMapping("/getConstants")
    @ApiOperation("常量获取")
    public InfectedWarningRuleConstants getConstants(){

        return new InfectedWarningRuleConstants();
    }

    @GetMapping("/getRuleDetail")
    @ApiOperation("法定传染-预警规则详情 ")
    public PageInfo<TbCdcmrInfectedDiseaseWarningRule> getRuleDetail(@RequestParam String diseaseInfoId,
                                                                     @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                     @RequestParam(defaultValue = "20") Integer pageSize,
                                                                     @RequestParam(required = false) String riskLevel,
                                                                     @RequestParam(required = false) String warningMethod,
                                                                     @RequestParam(required = false) String followStatus){

        return infectedDiseaseWarningRuleService.getRuleDetail(diseaseInfoId, pageIndex, pageSize, riskLevel, warningMethod, followStatus);
    }

    @GetMapping("/getInfectedProcessScope")
    @ApiOperation("获取传染病病例范围")
    public List<InfectedProcessScopeVO> getInfectedProcessScope(@RequestParam String diseaseId){

        return infectedDiseaseWarningRuleService.getInfectedProcessScope(diseaseId);
    }

    @PostMapping("/getInfectedTree")
    @ApiOperation("获取传染病结构树")
    public List<TreeNode> getInfectedTree(@RequestBody InfectedWarningRuleQueryDTO dto){

        return infectedDiseaseWarningService.getInfectedTree(dto);
    }

    @PostMapping("/savaOrUpdateRule")
    @ApiOperation("编辑传染病规则")
    @OperationLogAnnotation(operationName = "编辑传染病规则")
    public void savaOrUpdateRule(@RequestBody TbCdcmrInfectedDiseaseWarningRule rule){

        infectedDiseaseWarningRuleService.savaOrUpdateRule(rule);
    }
    
}

