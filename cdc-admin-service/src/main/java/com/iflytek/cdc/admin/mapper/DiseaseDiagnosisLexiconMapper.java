package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.SearchDiagnosisLexiconDTO;
import com.iflytek.cdc.admin.entity.DisLexicon;
import com.iflytek.cdc.admin.entity.DiseaseDiagnosisLexicon;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DiseaseDiagnosisLexiconMapper extends BaseMapper<DiseaseDiagnosisLexicon> {
    List<DiseaseDiagnosisLexicon> getDiagnosisLexiconPage(@Param("dto") SearchDiagnosisLexiconDTO dto);
}
