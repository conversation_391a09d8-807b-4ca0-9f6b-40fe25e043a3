package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.dto.UpdateInfecDTO;
import com.iflytek.cdc.admin.entity.InfectiousDiagnosis;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName InfectiousDiagnosisMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 10:46
 * @Version 1.0
 */
public interface InfectiousDiagnosisMapper {
    int deleteByPrimaryKey(String id);

    int insert(InfectiousDiagnosis record);

    int insertSelective(InfectiousDiagnosis record);

    InfectiousDiagnosis selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(InfectiousDiagnosis record);

    int updateByPrimaryKey(InfectiousDiagnosis record);

    List<InfectiousDiagnosis> queryInfecInfo(SearchInfecInfoDTO searchInfecInfoDTO);

    void insertInfectiousDiagnosis(@Param("infectiousDiagnosis") List<InfectiousDiagnosis> infectiousDiagnosis);

    void updateInfecInfo(UpdateInfecDTO updateInfecDTO);

    void updateinfectiousDiagnosis(InfectiousDiagnosis infectiousDiagnosis);

    InfectiousDiagnosis queryInfoById(String id);

    void updateCodeName(@Param("updateCode") String  code,@Param("updateCodeName") String codeName,@Param("updateUser") String updateUser);
}