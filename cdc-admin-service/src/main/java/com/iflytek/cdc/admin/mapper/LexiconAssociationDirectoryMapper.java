package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.LexiconAssociationDirectory;

import java.util.List;

public interface LexiconAssociationDirectoryMapper extends BaseMapper <LexiconAssociationDirectory>{
    int insert(LexiconAssociationDirectory record);

    LexiconAssociationDirectory selectByPrimaryKey(String id);

    List<LexiconAssociationDirectory> selectAll();
}