package com.iflytek.cdc.admin.model.mr.vo;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Data
public class TreeNode implements Comparable<TreeNode> {

    @ApiModelProperty(value = "id")
    protected String id;

    @ApiModelProperty(value = "value")
    protected String label;

    @ApiModelProperty(value = "code")
    protected String value;

    @ApiModelProperty(value = "parentId")
    protected String parentId;

    @ApiModelProperty(value = "orderFlag")
    protected Integer orderFlag;

    @ApiModelProperty(value = "childNodes")
    protected List<TreeNode> children;

    @ApiModelProperty(value = "节点额外属性")
    private TreeNodeProperty property;

    private Object originalData;

    /**
     * 能否选择 0:否 1:是
     */
    private String selectEnable;

    @Override
    public int compareTo(@NotNull TreeNode o) {

        return this.getValue().compareTo(o.getValue());
    }

    public boolean isRoot() {
        return StringUtils.isBlank(getParentId());
    }

    public static void visit(List<TreeNode> treeNodes, Consumer<TreeNode> visitor) {
        for (TreeNode treeNode : treeNodes) {
            visitor.accept(treeNode);
            List<TreeNode> children = treeNode.getChildren();
            if (CollectionUtil.isNotEmpty(children)) {
                visit(children, visitor);
            }
        }
    }

    public static <T extends TreeNode> List<T> buildTree(List<T> treeNodes) {
        // Partition nodes into roots and non-roots
        Map<Boolean, List<T>> partitionedNodes = treeNodes.stream().collect(Collectors.partitioningBy(TreeNode::isRoot));
        // Use Optional to handle potential null values
        List<T> roots = Optional.ofNullable(partitionedNodes.get(true)).orElseGet(ArrayList::new);
        Map<String, List<T>> leafMap = Optional.ofNullable(partitionedNodes.get(false))
                                               .orElseGet(ArrayList::new)
                                               .stream()
                                               .collect(Collectors.groupingBy(TreeNode::getParentId));
        // Build the tree recursively
        roots.forEach(root -> buildTree(root, leafMap));
        return roots;
    }

    private static <T extends TreeNode> void buildTree(T parent, Map<String, List<T>> leafMap) {
        List<T> children = leafMap.get(parent.getId());
        if (children != null) {
            parent.setChildren(new ArrayList<>(children));
            for (T child : children) {
                buildTree(child, leafMap); // 递归调用
            }
        }
    }

    public static <T> TreeNode from(T t,
                                    Function<T, String> idFunction,
                                    Function<T, String> parentFunction,
                                    Function<T, String> labelFunction,
                                    Function<T, String> valueFunction,
                                    Function<T, Integer> sortFunction) {
        return from(t, idFunction, parentFunction, labelFunction, valueFunction, sortFunction, false);
    }


    public static <T> TreeNode from(T t,
                                    Function<T, String> idFunction,
                                    Function<T, String> parentFunction,
                                    Function<T, String> labelFunction,
                                    Function<T, String> valueFunction,
                                    Function<T, Integer> sortFunction,
                                    boolean needOriginal) {
        TreeNode vo = new TreeNode();
        vo.setId(idFunction.apply(t));
        vo.setParentId(parentFunction.apply(t));
        vo.setLabel(labelFunction.apply(t));
        vo.setValue(valueFunction.apply(t));
        vo.setOrderFlag(sortFunction != null ? sortFunction.apply(t) : null);
        if (needOriginal) {
            vo.setOriginalData(t);
        }
        return vo;
    }

    public static TreeNode from(String id, String label, String value) {
        TreeNode vo = new TreeNode();
        vo.setId(id);
        vo.setLabel(label);
        vo.setValue(value);
        return vo;
    }

    public static TreeNode from( String label, String value){
       return from(null, label, value);
    }

    public static <T> List<TreeNode> buildTreeByNodeList(List<T> entities,
                                                         Function<T, String> idFunction,
                                                         Function<T, String> parentFunction,
                                                         Function<T, String> labelFunction,
                                                         Function<T, String> valueFunction,
                                                         Function<T, Integer> sortFunction,
                                                         boolean needOriginal) {

        //获取父节点 默认父id为空的节点是父节点
        List<T> parentNode = entities.stream()
                                     .filter(e -> StringUtils.isBlank(parentFunction.apply(e)))
                                     .collect(Collectors.toList());
        //获取所有子节点
        List<T> childNodes = entities.stream()
                                     .filter(e -> StringUtils.isNotBlank(parentFunction.apply(e)))
                                     .collect(Collectors.toList());
        //根节点
        List<TreeNode> rootNode = convertToTreeNode(parentNode, idFunction, parentFunction, labelFunction, valueFunction, sortFunction, needOriginal);
        //叶子节点
        List<TreeNode> leafNodes = convertToTreeNode(childNodes, idFunction, parentFunction, labelFunction, valueFunction, sortFunction, needOriginal);
        for (TreeNode treeNode : rootNode) {
            buildTree(treeNode, leafNodes);
        }
        return rootNode;
    }

    //构建树
    public static <T> List<TreeNode> buildTreeByNodeList(List<T> entities,
                                                         Function<T, String> idFunction,
                                                         Function<T, String> parentFunction,
                                                         Function<T, String> labelFunction,
                                                         Function<T, String> valueFunction) {

        return buildTreeByNodeList(entities, idFunction, parentFunction, labelFunction, valueFunction, null, false);
    }

    //构建树 处理需要排序的树结构
    public static <T> List<TreeNode> buildSortTreeByNodeList(List<T> entities,
                                                             Function<T, String> idFunction,
                                                             Function<T, String> parentFunction,
                                                             Function<T, String> labelFunction,
                                                             Function<T, String> valueFunction, 
                                                             Function<T, Integer> sortFunction) {

        return buildTreeByNodeList(entities, idFunction, parentFunction, labelFunction, valueFunction, sortFunction, false);
    }

    /**
     * 递归构造树结构
     * */
    public static <T> void buildTree(TreeNode currNode, List<TreeNode> treeNodeList) {

        List<TreeNode> childNodes = new ArrayList<>();
        for (TreeNode treeNode : treeNodeList) {
            if (Objects.equals(currNode.getId(), treeNode.getParentId())) {
                buildTree(treeNode, treeNodeList);
                childNodes.add(treeNode);
            }
        }
        currNode.setChildren(childNodes);
    }

    /**
     * 实体类转树节点
     * */
    public static <T> List<TreeNode> convertToTreeNode(List<T> entities,
                                                       Function<T, String> idFunction,
                                                       Function<T, String> parentFunction,
                                                       Function<T, String> labelFunction,
                                                       Function<T, String> valueFunction) {

        return convertToTreeNode(entities, idFunction, parentFunction, labelFunction, valueFunction, null, false);
    }

    /**
     * 实体类转树节点
     * */
    public static <T> List<TreeNode> convertToTreeNode(List<T> entities,
                                                       Function<T, String> idFunction,
                                                       Function<T, String> parentFunction,
                                                       Function<T, String> labelFunction,
                                                       Function<T, String> valueFunction,
                                                       Function<T, Integer> sortFunction,
                                                       boolean needOriginal) {

        List<TreeNode> nodeList = new ArrayList<>();
        for (T t : entities) {

            TreeNode treeNode = TreeNode.from(t, idFunction, parentFunction, labelFunction, valueFunction, sortFunction, needOriginal);
            nodeList.add(treeNode);
        }
        return nodeList;
    }

    /**
     * 将树的叶子节点的children全部设为null
     * */
    public static void setChildrenToNull(List<TreeNode> nodes) {
        for (TreeNode node : nodes) {
            if (node.getChildren() == null || node.getChildren().isEmpty()) {
                node.setChildren(null);
            } else {
                setChildrenToNull(node.getChildren());
            }
        }

    }

    /**
     * 遍历树，找到匹配的节点，并把该节点下所有子节点的id添加到list中
     * */
    public static void getAllNodeValue(List<String> idList, TreeNode root, String id) {

        if (root == null) {
            return;
        }
        if (Objects.equals(id, root.getId())) {
            addAllNodes(idList, root);
        } else {
            if (root.getChildren() != null) {
                for (TreeNode childNode : root.getChildren()) {
                    getAllNodeValue(idList, childNode, id);
                }
            }
        }
    }

    /**
     * 病历树，获取某个节点下所有子节点的id
     * */
    private static void addAllNodes(List<String> idList, TreeNode root) {
        if (root == null) {
            return;
        }
        idList.add(root.getId());
        if (root.getChildren() != null) {
            for (TreeNode treeNode : root.getChildren()) {
                addAllNodes(idList, treeNode);
            }
        }
    }

    /**
     * 遍历树，找到匹配的节点并返回该节点
     * */
    public static TreeNode getMatchNodeById(List<TreeNode> roots, Predicate<TreeNode> predicate) {
        TreeNode node = null;
        for (TreeNode treeNode : roots) {
            node = getMatchNodeById(treeNode, predicate);
            if (node != null) {
                break;
            }
        }
        return node;
    }

    /**
     * 遍历树，找到匹配的节点并返回该节点
     * */
    public static TreeNode getMatchNodeById(TreeNode root, Predicate<TreeNode> predicate) {

        if (root == null) {
            return null;
        }

        if (predicate.test(root)) {
            return root;
        }
        if (root.getChildren() != null) {
            for (TreeNode node : root.getChildren()) {
                TreeNode target = getMatchNodeById(node, predicate);
                if (target != null) {
                    return target;
                }
            }
        }
        return null;
    }

    /**
     * 判断森林最大深度
     * */
    public static Integer getForestMaxDepth(List<TreeNode> root) {

        if (CollectionUtil.isEmpty(root)) {
            return 0;
        }
        int depth = -1;
        for (TreeNode treeNode : root) {
            depth = Math.max(depth, maxDepth(treeNode));
        }
        return depth;
    }

    private static int maxDepth(TreeNode node) {

        if (node == null) {
            return 0;
        }
        if (node.getChildren() == null || CollectionUtil.isEmpty(node.getChildren())) {
            return 1;
        }
        int maxDepth = 0;
        for (TreeNode child : node.getChildren()) {
            maxDepth = Math.max(maxDepth, maxDepth(child));
        }
        return maxDepth + 1;
    }

    /**
     * 树 根据id排序
     * */
    public static void sortTree(List<TreeNode> root) {

        if (root == null || root.isEmpty()) {
            return;
        }
        //对当前节点的子节点进行排序
        root.sort(Comparator.nullsLast(Comparator.comparing(TreeNode::getId)));
        // 遍历所有根节点
        for (TreeNode node : root) {
            // 递归对子节点进行排序
            List<TreeNode> children = node.getChildren();
            sortTree(children);
            // 排序后的子节点需要重新设置回当前节点
            node.setChildren(children);
        }
    }

    /**
     * 通用方法，list对象转树结构
     * */
    public  static <T> List<TreeNode> groupByList(List<T> list,
                                          Function<T, String> labelFunction,
                                          Function<T, String> valueFunction,
                                          Function<Map.Entry<TreeNode, List<T>>, List<TreeNode>> childFunction) {
        return list.stream()
                .collect(Collectors.groupingBy(e -> TreeNode.from(labelFunction.apply(e), valueFunction.apply(e)), LinkedHashMap::new, Collectors.toList()))
                .entrySet()
                .stream()
                .map(entry -> {
                    entry.getKey().setChildren(childFunction == null ? null : childFunction.apply(entry));
                    return entry.getKey();
                })
                .collect(Collectors.toList());
    }

    /**
     * 树 根据排序字段
     * */
    public static void sortTreeBy(List<TreeNode> root,
                                  Function<TreeNode, Integer> sortFunction) {

        if (root == null || root.isEmpty()) {
            return;
        }
        //对当前节点的子节点进行排序
        root.sort(Comparator.nullsLast(Comparator.comparing(sortFunction, Comparator.nullsLast(Comparator.naturalOrder())))
                                                 .thenComparing(TreeNode::getId, Comparator.nullsLast(Comparator.naturalOrder())));
        // 遍历所有根节点
        for (TreeNode node : root) {
            // 递归对子节点进行排序
            List<TreeNode> children = node.getChildren();
            sortTreeBy(children, sortFunction);
            // 排序后的子节点需要重新设置回当前节点
            node.setChildren(children);
        }
    }

}
