package com.iflytek.cdc.admin.service.mdm;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.dto.ResponseResult;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageData;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermDictInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermDictInfoFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName MdmDataSyncHandler
 * @Description mdm逻辑操作业务类
 * <AUTHOR>
 * @Date 2021/9/18 14:10
 * @Version 1.0
 */

@Service("mdmDataSyncHandler")
@Slf4j
public class MdmDataSyncHandler {

    @Resource
    private MdmDataSyncFactory mdmDataSyncFactory;

    @Resource
    public MdmDataSyncService mdmDataSyncService;

    public ResponseResult executeSyncMdmData(String code, String loginUserId, boolean flag) {
        return  ((MdmDataSyncExecute)mdmDataSyncFactory.getSyncBean(code)).executeSyncMdmData(code,loginUserId,flag);
    }

    public String   syncMdmGetStatus(String code,String  batchId,String loginUserId){
        return ((MdmDataSyncAndGetStatus) mdmDataSyncFactory.getSyncBean(code)).syncMdmDataGetStatus(code,loginUserId,batchId);
    }

    public ResponseResult getMdmDicData(String code){
        //判断字典目录是否存在
        if (isCanUse(code)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
        }
        List<TermCodedValueInfo> mdmCodeValues = mdmDataSyncService.mdmDictCodeSync(code,
                MdmDataSyncConstants.SYNDROME_PAGENUMBER, MdmDataSyncConstants.SYNDROME_PAGESIZE);
        return new ResponseResult(mdmCodeValues);
    }

    public boolean isCanUse(String originCode) {
        //查询字典目录
        MdmPageData<TermDictInfo, TermDictInfoFilter> mdmPageData = mdmDataSyncService.getMdmTermDictInfo(originCode);
        List<TermDictInfo> dictInfos = mdmPageData.getEntities();
        //判断为空
        if(CollUtil.isEmpty(dictInfos)){
            return true;
        }
        //如果被删除
        if(MdmDataSyncConstants.MDM_YES_DELETE.equals(dictInfos.get(0).getDeleted().toString())){
            return true;
        }
        //判断是否被禁用
        return dictInfos.get(0).getEnabled().toString().equals(MdmDataSyncConstants.NO_USE);
    }

}
