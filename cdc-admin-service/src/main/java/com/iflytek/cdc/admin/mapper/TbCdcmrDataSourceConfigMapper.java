package com.iflytek.cdc.admin.mapper;


import com.iflytek.cdc.admin.dto.DataSourceConfigVO;
import com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrDataSourceConfigMapper {

    TbCdcmrDataSourceConfig selectByBusinessTypeAndSignalType(@Param("businessType") String businessType, @Param("signalType") String signalType);

    List<TbCdcmrDataSourceConfig> listByBusinessType(@Param("businessType") String businessType);

    List<DataSourceConfigVO> getList();

    int updateByPrimaryKey(TbCdcmrDataSourceConfig record);
}