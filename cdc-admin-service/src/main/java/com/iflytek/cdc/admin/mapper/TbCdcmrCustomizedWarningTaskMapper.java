package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.CustomizedWarningTaskQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarningTaskVO;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask;

import java.util.List;

public interface TbCdcmrCustomizedWarningTaskMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrCustomizedWarningTask record);

    int insertSelective(TbCdcmrCustomizedWarningTask record);

    CustomizedWarningTaskVO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrCustomizedWarningTask record);

    int updateByPrimaryKey(TbCdcmrCustomizedWarningTask record);

    List<CustomizedWarningTaskVO> getList(CustomizedWarningTaskQueryDTO queryDTO);

    List<TbCdcmrCustomizedWarningTask> selectByWarnRuleId(String warnRuleId);

    List<CustomizedWarningTaskVO> getEnabledWarn();

    TbCdcmrCustomizedWarningTask getByName(String name);

    String getWarningIdByTaskId(String taskId);
}