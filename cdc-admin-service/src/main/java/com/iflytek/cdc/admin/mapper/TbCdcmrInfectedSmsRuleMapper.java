package com.iflytek.cdc.admin.mapper;


import com.iflytek.cdc.admin.dto.TbCdcmrInfectedSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcmrInfectedSmsRuleMapper {
    int insert(List<TbCdcmrInfectedSmsRule> record);

    int updateByPrimaryKeySelective(List<TbCdcmrInfectedSmsRule> recordList);
    int deleteByInfectedCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                                 @Param("loginUserId") String loginUserId, @Param("infectedCodeList") List<String> infectedCodeList);

    List<TbCdcmrInfectedSmsRule> getListByLoginUserId(String loginUserId);

    List<TbCdcmrInfectedSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                                 @Param("loginUserId") String loginUserId);



}