package com.iflytek.cdc.admin.service.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseWarningRule;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.model.mr.vo.InfectedProcessScopeVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 传染病预警-传染病预警规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
public interface InfectedDiseaseWarningRuleService extends IService<TbCdcmrInfectedDiseaseWarningRule> {

    /**
     * 查看传染病配置的规则
     * */
    PageInfo<TbCdcmrInfectedDiseaseWarningRule> getRuleDetail(String diseaseInfoId, Integer pageIndex, Integer pageSize, String riskLevel, String warningMethod, String followStatus);

    /**
     * 获取传染病病例范围
     * */
    List<InfectedProcessScopeVO> getInfectedProcessScope(String diseaseId);

    /**
     * 新增 or 更新传染病规则
     * */
    void savaOrUpdateRule(TbCdcmrInfectedDiseaseWarningRule rule);

}
