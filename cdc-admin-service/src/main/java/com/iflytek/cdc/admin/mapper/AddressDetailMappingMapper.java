package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.AddressDetailMapping;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
public interface AddressDetailMappingMapper extends BaseMapper<AddressDetailMapping> {

    /**
     * 存在就更新 不存在就新增
     *
     * @param addressDetailMapping
     */
    void saveOrUpdateAddressDetailMapping(AddressDetailMapping addressDetailMapping);

    void batchUpdateSimilarity(@Param("list") List<AddressDetailMapping> addressDetailMappingList);

    List<AddressDetailMapping> selectByEtlTimeLimit(Date startDate,
                                                    Date endDate,
                                                    Integer status,
                                                    Double similarity,
                                                    Integer pageIndex,
                                                    Integer pageSize);
}
