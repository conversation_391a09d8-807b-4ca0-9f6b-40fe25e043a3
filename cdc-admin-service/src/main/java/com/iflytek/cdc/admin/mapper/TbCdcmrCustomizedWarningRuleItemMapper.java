package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRuleItem;

import java.util.List;

public interface TbCdcmrCustomizedWarningRuleItemMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrCustomizedWarningRuleItem record);

    int insertSelective(TbCdcmrCustomizedWarningRuleItem record);

    TbCdcmrCustomizedWarningRuleItem selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrCustomizedWarningRuleItem record);

    int updateByPrimaryKey(TbCdcmrCustomizedWarningRuleItem record);

    int deleteByRuleId(String ruleId);

    int batchInsert(List<TbCdcmrCustomizedWarningRuleItem> recordList);

    List<TbCdcmrCustomizedWarningRuleItem> selectByRuleId(String ruleId);

}