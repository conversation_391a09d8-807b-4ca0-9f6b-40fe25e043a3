package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto;
import com.iflytek.cdc.admin.dto.UpdateDistrictDto;
import com.iflytek.cdc.admin.entity.ClientUpgradeDistrict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName ClientUpgradeDistrictMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/12 15:38
 * @Version 1.0
 */
public interface ClientUpgradeDistrictMapper {
    int deleteByPrimaryKey(String id);

    int insertSelective(ClientUpgradeDistrict record);

    ClientUpgradeDistrict selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ClientUpgradeDistrict record);

    int updateByPrimaryKey(ClientUpgradeDistrict record);

    int addBatchClientUpgradeDistrictDto( @Param("clientUpgradeDistrictDtos") List<ClientUpgradeDistrict> clientUpgradeDistrictDtos);

    void deleteOrgByCode(UpdateDistrictDto updateDistrictDto);

    List<ClientUpgradeUrlDto> getOrgCodeById(UpdateDistrictDto updateDistrictDto);
}