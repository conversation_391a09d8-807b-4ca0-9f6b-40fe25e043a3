package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.typehandler.ArrayListJsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName(schema ="app",  value = "tb_cdcmr_disease_permission")
public class DiseasePermission implements Serializable {

    private static final long serialVersionUID = 1L;


    public static final String TB_NAME="tb_cdcmr_disease_permission";


    private String id;

    private String orgId;

    private String subsystemCode;

    private String infectedAllFlag;

    @TableField(typeHandler = ArrayListJsonTypeHandler.class)
    private List<DiseaseInfoData> infectedDiseaseArr;


    private String syndromeAllFlag;

    @TableField(typeHandler = ArrayListJsonTypeHandler.class)
    private List<DiseaseInfoData> syndromeDiseaseArr;

    private Date createTime;

    private Date updateTime;

    private String creatorId;

    private String creator;

    private String updaterId;

    private String updater;

    private String infectedDiseaseInfo;

    private String syndromeDiseaseInfo;


    @Data
    public static class DiseaseInfoData {
        private String diseaseName;

        private String diseaseCode;
    }
}
