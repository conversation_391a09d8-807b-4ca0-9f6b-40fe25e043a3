package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import com.iflytek.cdc.admin.mapper.TbCdcAttachmentMapper;
import com.iflytek.cdc.admin.service.FileService;
import com.iflytek.cdc.admin.util.FileUtils;
import com.iflytek.cdc.admin.util.PCMUtil;
import com.iflytek.cdc.admin.util.StorageClientUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件服务类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {
    @Value("${sound.temp.path:/home/<USER>/audios/}")
    private String tempAudioPath;
    @Autowired
    private StorageClientUtil storageClientUtil;

    @Autowired
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Autowired
    private BatchUidService batchUidService;

    @Value("${storage.switch:swift}")
    private String storageSwitch;
    @Value("${swift.prefix:/file-obj}")
    private String swiftPrefix;

    @Value("${minio.prefix:/minIoFile}")
    private String minioPrefix;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public List<String> upload(MultipartFile file) {
        return Arrays.asList(executeUpload(file));
    }

    @Override
    public String upload(String fileUrl, String businessId) {

//        try {
//            URL url = new URL(fileUrl);
//            URLConnection conn = url.openConnection();
//            InputStream inStream = conn.getInputStream();
//            String fileName = System.currentTimeMillis() + "_" +fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
//            return executeUpload(inStream, businessId, fileName);
//        } catch (Exception e) {
//            log.error("上传音频失败", e);
//            //更新个案已经获取到url
//            //重置当前url记录
//            throw new MedicalBusinessException("上传音频失败");
//        }
        String fileName = System.currentTimeMillis() + "_" +fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
        String tempAudio = tempAudioPath + fileName;
        try(InputStream in = new BufferedInputStream(new URL(fileUrl).openStream())){
            MultipartFile file = new MockMultipartFile("file", fileName, "audio/wav", IOUtils.toByteArray(in));
            String tempAudio2 = tempAudioPath + fileName;
            PCMUtil.convert16kAudioFiles(file, tempAudio2);
            PCMUtil.get16KUrl(tempAudio2, tempAudio);
            file = new MockMultipartFile("file", fileName, "audio/wav", new FileInputStream(tempAudio));
//                    swiftService.uploadFileAsync(file, caseId, audioFileName);
//                if (StrUtil.equals(address, "call")) {
//                    fileService.uploadFileAsync(file, businessId, audioFileName);
//                } else {
            String attachmentId =  upload(file.getInputStream() , businessId, fileName);
            //2、新增记录
            File audioFile = new File(tempAudio);
            audioFile.delete();
            return  attachmentId;
        }catch (Exception e){
            log.error("上传音频失败", e);
            throw new MedicalBusinessException("上传音频失败");
        }
    }

    @Override
    public String upload(InputStream inputStream, String businessId, String fileName) {
        return executeUpload(inputStream, businessId,  fileName);
    }

    @Override
    @Async("asyncExecutor")
    public void uploadFileAsync(MultipartFile file, String businessId, String fileName) {
        try(InputStream inputStream = file.getInputStream()) {
            executeUpload(inputStream, businessId, fileName);
        } catch (Exception e) {
            log.error("异步上传文件失败", e);
            throw new MedicalBusinessException("11458002", "文件上传过程中发生未知错误");
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String upload(byte[] bytes, String fileName) {
        String uid = String.valueOf(batchUidService.getUid("tb_cdcmr_attachment"));
        String filePath = "";
        try {
            //0, 构建存储对象名称
            String objectName = storageClientUtil.getObjectName(uid, fileName);

            //1，上传文件到SWIFT服务器
            filePath = storageClientUtil.putObject(bytes, objectName, false);

            //2，将文件路径存储到数据库
            TbCdcAttachment tbCdcAttachment = TbCdcAttachment.builder()
                    .id(uid)
                    .attachmentName(fileName)
                    .attachmentPath(filePath)
                    .createTime(new Date())
                    .status(Constants.STATUS_ON).build();
            tbCdcAttachmentMapper.insert(tbCdcAttachment);
        } catch (Exception e) {
            log.error("文件上传错误", e);
            throw new MedicalBusinessException("11458001", "文件上传错误");
        }
        return uid;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public List<String> uploads(MultipartFile[] files) {
        List<String> idList = new ArrayList<>();
        for (MultipartFile file : files) {
            Set<String> fileType = Stream.of("rar", "zip", "doc", "docx", "pdf", "jpg","jpeg","png", "xls", "xlsx").collect(Collectors.toSet());
            try {
                if (!fileType.contains(Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1).toLowerCase(Locale.ROOT))) {
                    throw new MedicalBusinessException("11458002", "文件格式错误！");
                }
            } catch (Exception e) {
                throw new MedicalBusinessException("11458002", "文件格式错误！");
            }
            idList.add(executeUpload(file));
        }
        return idList;
    }

    @Override
    public ResponseEntity<byte[]> download(String id) {
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(id);
        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        ByteArrayOutputStream byteArrayOutputStream = executeDownload(objectName);
        if (!Objects.isNull(byteArrayOutputStream)) {
            return new ResponseEntity<>(byteArrayOutputStream.toByteArray(), FileUtils.getHttpHeaders(tbCdcAttachment.getAttachmentName()), HttpStatus.CREATED);
        }
        return new ResponseEntity<>(null, null, HttpStatus.NO_CONTENT);
    }


        /**
     * 文件上传处理
     *
     * @param file MultipartFile 文件对象
     * @return 文件唯一标识符
     */
    private String executeUpload(MultipartFile file){
        return executeUploadCommon(() -> {
            try{
                return file.getInputStream();
            } catch (IOException e) {
                throw new MedicalBusinessException("文件上传错误");
            }

        }, file.getOriginalFilename(), null);
    }

    /**
     * 文件上传处理
     *
     * @param fileInputStream 文件输入流
     * @param businessId      业务ID
     * @param fileName        文件名
     * @return 文件唯一标识符
     */
    private String executeUpload(InputStream fileInputStream, String businessId, String fileName) {
        return executeUploadCommon(() -> fileInputStream, fileName, businessId);
    }

    /**
     * 文件上传处理的公共逻辑
     *
     * @param inputStreamSupplier 提供文件输入流的函数式接口
     * @param fileName            文件名
     * @param businessId          业务ID（可选）
     * @return 文件唯一标识符
     */
    private String executeUploadCommon(Supplier<InputStream> inputStreamSupplier, String fileName, String businessId) {
        String uniqueId = String.valueOf(batchUidService.getUid("tb_cdcmr_attachment"));
        String filePath;
        try (InputStream fileInputStream = inputStreamSupplier.get()) {
            // 构建存储对象名称
            String objectName = storageClientUtil.getObjectName(uniqueId, fileName);

            // 上传文件到SWIFT服务器
            filePath = storageClientUtil.putObject(fileInputStream, objectName);

            // 将文件路径存储到数据库
            TbCdcAttachment tbCdcAttachment = TbCdcAttachment.builder()
                    .id(uniqueId)
                    .attachmentName(fileName)
                    .analysisId(businessId)
                    .attachmentPath(filePath)
                    .createTime(new Date())
                    .status(Constants.STATUS_ON).build();
            tbCdcAttachmentMapper.insert(tbCdcAttachment);
        } catch (IOException e) {
            log.error("文件上传错误 - 文件名: {}, UID: {}", fileName, uniqueId, e);
            throw new MedicalBusinessException("11458001", "文件上传错误");
        } catch (Exception e) {
            log.error("文件上传错误 - 文件名: {}, UID: {}", fileName, uniqueId, e);
            throw new MedicalBusinessException("11458002", "文件上传过程中发生未知错误");
        }
        return uniqueId;
    }

    @Override
    public TbCdcAttachment saveAttachmentRecord(TbCdcAttachment attachment) {
        if (StrUtil.isBlank(attachment.getId())){
            String uid = String.valueOf(batchUidService.getUid("tb_cdcmr_attachment"));
            attachment.setId(uid);
        }
        attachment.setCreateTime(new Date());
        attachment.setStatus(Constants.STATUS_ON);

        tbCdcAttachmentMapper.insert(attachment);
        return attachment;
    }

    /**
     * 文件下载
     *
     * @param objName 文件名称
     */
    public ByteArrayOutputStream executeDownload(String objName) {
        StorageObject storageObject = storageClientUtil.getObject(objName);
        if (Objects.isNull(storageObject)) {
            throw new MedicalBusinessException("11458003", "文件不存在");
        }

        InputStream inputStream = storageObject.getInputStream();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
        } catch (IOException e) {
            log.error("文件流转换失败", e);
            throw new MedicalBusinessException("11458004", "文件下载失败");
        }
        try {
            inputStream.close();
        } catch (IOException e) {
            log.error("关闭文件流失败", e);
            throw new MedicalBusinessException("11458004", "文件下载失败");
        }

        return baos;
    }
    
    public String getPathByAttachmentId(String id){
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(id);
        if (tbCdcAttachment == null){
            throw new MedicalBusinessException("11458003", "文件不存在");
        }
        String attachmentPath = tbCdcAttachment.getAttachmentPath();
        if ("swift".equals(storageSwitch)){
            attachmentPath = attachmentPath.startsWith(swiftPrefix) ? attachmentPath : swiftPrefix.concat(attachmentPath);
        } else {
            attachmentPath = attachmentPath.startsWith(minioPrefix) ? attachmentPath : minioPrefix.concat(attachmentPath);
        }
        return attachmentPath;

    }

    /**
     * 
     * @param file
     * @return
     */
    @Override
    public  String fileUploadGetUrl(MultipartFile file){
        //上传文件
        String fileId = executeUpload(file);
        //获取文件路径
        String filePath = getPathByAttachmentId(fileId);
        return filePath;
    }
}
