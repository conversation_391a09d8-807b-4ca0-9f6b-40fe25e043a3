package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.ReportRemindTimeGapConfigEditDTO;
import com.iflytek.cdc.admin.entity.ReportRemindTimeGapConfig;
import com.iflytek.cdc.admin.mapper.ReportRemindTimeGapConfigMapper;
import com.iflytek.cdc.admin.service.ReportRemindTimeGapConfigService;
import com.iflytek.cdc.admin.vo.ReportRemindTimeGapConfigVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapOrganization;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgDto;
import com.iflytek.zhyl.uap.usercenter.service.UapOrgApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


@Slf4j
@Service
public class ReportRemindTimeGapConfigServiceImpl
        extends ServiceImpl<ReportRemindTimeGapConfigMapper, ReportRemindTimeGapConfig>
        implements ReportRemindTimeGapConfigService {


    private final static String INSERT_REPORT = "新增报告卡";

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }


    private UapOrgApi uapOrgApi;

    @Autowired
    public void setUapOrgApi(UapOrgApi uapOrgApi) {
        this.uapOrgApi = uapOrgApi;
    }

    @Override
    public ReportRemindTimeGapConfig queryByOrgId(String orgId,String loginUserId) {
        // 根据orgId进行查询
        LambdaQueryWrapper<ReportRemindTimeGapConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportRemindTimeGapConfig::getOrgId,orgId);
        ReportRemindTimeGapConfig one = this.getOne(wrapper);
        if (one == null){
            log.error("当前配置为空,进行初始化数据 orgId: {}", orgId);
            // 进行初始化数据
            one = buildReportRemindTimeGapConfig(orgId,loginUserId);
        }
        return one;
    }

    private ReportRemindTimeGapConfig buildReportRemindTimeGapConfig(String orgId, String loginUserId) {

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        if (user == null){
            throw new MedicalBusinessException("查询用户失败");
        }
        ReportRemindTimeGapConfig reportRemindTimeGapConfig = new ReportRemindTimeGapConfig();
        reportRemindTimeGapConfig.setId(String.valueOf(batchUidService.getUid(ReportRemindTimeGapConfig.TB_NAME)));

        reportRemindTimeGapConfig.setPositionType(INSERT_REPORT);
        reportRemindTimeGapConfig.setTimeGap(12);
        reportRemindTimeGapConfig.setTimeUnit("month");
        reportRemindTimeGapConfig.setOrgId(orgId);

        reportRemindTimeGapConfig.setCreator(user.getName());
        reportRemindTimeGapConfig.setCreatorId(loginUserId);
        reportRemindTimeGapConfig.setCreateTime(new Date());

        reportRemindTimeGapConfig.setUpdater(user.getName());
        reportRemindTimeGapConfig.setUpdaterId(loginUserId);
        reportRemindTimeGapConfig.setUpdateTime(new Date());

        UapOrgDto uapOrgDto = uapOrgApi.getExt(orgId);
        if (uapOrgDto != null){
            TUapOrganization uapOrganization = uapOrgDto.getUapOrganization();
            if (uapOrganization != null){
                String province = uapOrganization.getProvince();
                if (StrUtil.isNotBlank(province)){
                    reportRemindTimeGapConfig.setProvinceName(province);
                }
                String provinceCode = uapOrganization.getProvinceCode();
                if (StrUtil.isNotBlank(provinceCode)){
                    reportRemindTimeGapConfig.setProvinceCode(provinceCode);
                }

                String city = uapOrganization.getCity();
                if (StrUtil.isNotBlank(city)){
                    reportRemindTimeGapConfig.setCityName(city);
                }

                String cityCode = uapOrganization.getCityCode();
                if (StrUtil.isNotBlank(cityCode)) {
                    reportRemindTimeGapConfig.setCityCode(cityCode);
                }

                String district = uapOrganization.getDistrict();
                if (StrUtil.isNotBlank(district)) {
                    reportRemindTimeGapConfig.setDistrictName(district);
                }
                String districtCode = uapOrganization.getDistrictCode();
                if (StrUtil.isNotBlank(districtCode)) {
                    reportRemindTimeGapConfig.setDistrictCode(districtCode);
                }
            }
        }
        this.save(reportRemindTimeGapConfig);
        return reportRemindTimeGapConfig;
    }

    @Override
    public ReportRemindTimeGapConfig editById(ReportRemindTimeGapConfigEditDTO dto, String loginUserId) {
        if (dto == null){
            throw new MedicalBusinessException("缺少参数");
        }
        String id = dto.getId();
        if (StrUtil.isBlank(id)){
            throw new MedicalBusinessException("缺少配置Id");
        }
        Integer timeGap = dto.getTimeGap();
        if (timeGap == null){
            throw new MedicalBusinessException("缺少时间间隔");
        }
        if (timeGap < 1){
            throw new MedicalBusinessException("时间间隔需大于0");
        }
        ReportRemindTimeGapConfig reportRemindTimeGapConfig = this.getById(id);
        if (reportRemindTimeGapConfig == null){
            log.error("根据id未查询到报告提醒时间间隔配置 id:{} ", id);
            throw new MedicalBusinessException("配置不存在");
        }
        reportRemindTimeGapConfig.setTimeGap(timeGap);

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        reportRemindTimeGapConfig.setUpdater(user.getName());
        reportRemindTimeGapConfig.setUpdaterId(user.getId());
        reportRemindTimeGapConfig.setUpdateTime(new Date());

        this.updateById(reportRemindTimeGapConfig);
        return reportRemindTimeGapConfig;
    }

    @Override
    public ReportRemindTimeGapConfigVO queryByLoginUserId(String loginUserId) {
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        String orgId = user.getOrgId();
        if (StrUtil.isBlank(orgId)){
            return null;
        }
        LambdaQueryWrapper<ReportRemindTimeGapConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportRemindTimeGapConfig::getOrgId,orgId);
        ReportRemindTimeGapConfig one = this.getOne(wrapper);
        if (one == null){
            one = buildReportRemindTimeGapConfig(orgId,loginUserId);
        }
        ReportRemindTimeGapConfigVO reportRemindTimeGapConfigVO = new ReportRemindTimeGapConfigVO();
        BeanUtils.copyProperties(one,reportRemindTimeGapConfigVO);
        return reportRemindTimeGapConfigVO;
    }
}
