package com.iflytek.cdc.admin.model.mr.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SyndromeDiseaseInfoVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "疾病code")
    private String diseaseCode;

    @ApiModelProperty(value = "疾病name")
    private String diseaseName;

    @ApiModelProperty(value = "疾病父类id，如果没有父类则为空")
    private String diseaseParentId;

    @ApiModelProperty(value = "排序")
    private Integer orderFlag;

    private String selectEnable;
}
