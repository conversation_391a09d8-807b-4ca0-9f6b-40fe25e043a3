package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.CustomizedWarnQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarningRuleExportDataDto;
import com.iflytek.cdc.admin.dto.CustomizedWarningRuleVO;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule;

import java.util.List;

public interface TbCdcmrCustomizedWarningRuleMapper {
    int deleteByPrimaryKey(String id);

    int deleteByWarnId(String warnId);

    int insert(TbCdcmrCustomizedWarningRule record);

    int insertSelective(TbCdcmrCustomizedWarningRule record);

    CustomizedWarningRuleVO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrCustomizedWarningRule record);

    int updateByPrimaryKey(TbCdcmrCustomizedWarningRule record);

    TbCdcmrCustomizedWarningRule selectByWarnId(String warnId);

    List<CustomizedWarningRuleVO> getList(CustomizedWarnQueryDTO customizedWarnQueryDTO);

    List<CustomizedWarningRuleVO> getEnabledWarn(String warningType);

    List<CustomizedWarningRuleExportDataDto> getExportData();

}