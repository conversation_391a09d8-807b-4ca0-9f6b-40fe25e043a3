package com.iflytek.cdc.admin.mapper;


import com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcmrPoisonSmsRuleMapper {
    int insert(List<TbCdcmrPoisonSmsRule> record);

    int updateByPrimaryKeySelective(List<TbCdcmrPoisonSmsRule> recordList);

    int deleteByPoisonCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                               @Param("loginUserId") String loginUserId, @Param("poisonCodeList") List<String> poisonCodeList);

    List<TbCdcmrPoisonSmsRule> getListByLoginUserId(@Param("loginUserId") String loginUserId);

    List<TbCdcmrPoisonSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                            @Param("loginUserId") String loginUserId);
}