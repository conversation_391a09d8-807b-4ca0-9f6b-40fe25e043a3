package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.InspectionStandardQueryParam;
import com.iflytek.cdc.admin.dto.InspectionStandardVO;
import com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard;

import java.util.List;

public interface TbCdcmrInspectionStandardMapper {
    int deleteByPrimaryKey(String inspectionItemCode);

    int insert(TbCdcmrInspectionStandard record);

    int insertSelective(TbCdcmrInspectionStandard record);

    TbCdcmrInspectionStandard selectByPrimaryKey(String inspectionItemCode);

    int updateByPrimaryKeySelective(TbCdcmrInspectionStandard record);

    int updateByPrimaryKey(TbCdcmrInspectionStandard record);

    List<TbCdcmrInspectionStandard> getList();

    List<InspectionStandardVO> getListByParam(InspectionStandardQueryParam param);

    int deleteByPrimaryKeyList(List<String> inspectionItemCodeList);

    int upsertList(List<TbCdcmrInspectionStandard> recordList);

}