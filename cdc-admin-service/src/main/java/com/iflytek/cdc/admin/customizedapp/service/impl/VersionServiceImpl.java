package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsVersion;
import com.iflytek.cdc.admin.customizedapp.enums.VersionStatusEnum;
import com.iflytek.cdc.admin.customizedapp.enums.VersionTypeEnum;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsVersionMapper;
import com.iflytek.cdc.admin.customizedapp.service.VersionService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class VersionServiceImpl extends CdcServiceBaseImpl<TbCdccsVersionMapper, TbCdccsVersion> implements VersionService {

    @Override
    public void create(String businessId,
                       String businessKey,
                       String versionName,
                       VersionTypeEnum businessType) {
        TbCdccsVersion entity = new TbCdccsVersion();
        entity.setBusinessId(businessId);
        entity.setBusinessKey(businessKey);
        entity.setBusinessType(businessType.getCode());
        entity.setVersionName(versionName);
        entity.setStatus(VersionStatusEnum.WAITING_PUBLISH.getCode());
        create(entity);
    }

    @Override
    public TbCdccsVersion loadPublished(String businessKey, VersionTypeEnum businessType) {
        LambdaQueryWrapper<TbCdccsVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdccsVersion::getBusinessKey, businessKey)
                .eq(TbCdccsVersion::getBusinessType, VersionTypeEnum.APP_MENU.getCode())
                .eq(TbCdccsVersion::getStatus, VersionStatusEnum.PUBLISHED.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public String loadPublishedBusinessId(String businessKey, VersionTypeEnum businessType) {
        TbCdccsVersion tbCdccsVersion = loadPublished(businessKey, businessType);
        if (tbCdccsVersion == null){
            throw new MedicalBusinessException("未获取到版本");
        }
        return tbCdccsVersion.getBusinessId();
    }

    @Override
    public void published(String businessId, VersionTypeEnum businessType) {
        TbCdccsVersion tbCdccsVersion = loadByBusinessId(businessId, businessType);
        LambdaUpdateChainWrapper<TbCdccsVersion> updateChainWrapper = lambdaUpdate();
        updateChainWrapper.set(TbCdccsVersion::getStatus, VersionStatusEnum.CANCELLED.getCode())
                .eq(TbCdccsVersion::getBusinessKey, tbCdccsVersion.getBusinessKey())
                .eq(TbCdccsVersion::getStatus, VersionStatusEnum.PUBLISHED.getCode());
        updateChainWrapper.update();
        tbCdccsVersion.setStatus(VersionStatusEnum.PUBLISHED.getCode());
        tbCdccsVersion.setPublishTime(new Date());
        update(tbCdccsVersion);
    }

    @Override
    public TbCdccsVersion loadByBusinessId(String businessId, VersionTypeEnum businessType) {
        LambdaQueryWrapper<TbCdccsVersion> queryWrapper = lambdaQueryWrapper();
        queryWrapper.eq(TbCdccsVersion::getBusinessId, businessId);
        queryWrapper.eq(TbCdccsVersion::getBusinessType, businessType.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<TbCdccsVersion> listByBusinessKey(String businessKey, VersionTypeEnum businessType) {
        LambdaQueryWrapper<TbCdccsVersion> queryWrapper = lambdaQueryWrapper();
        queryWrapper.eq(TbCdccsVersion::getBusinessKey, businessKey);
        queryWrapper.eq(TbCdccsVersion::getBusinessType, businessType.getCode());
        queryWrapper.orderByDesc(TbCdccsVersion::getUpdateTime);
        return list(queryWrapper);
    }


}
