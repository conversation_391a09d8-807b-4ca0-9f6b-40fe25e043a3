package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.UnknownReasonQueryDto;
import com.iflytek.cdc.admin.dto.UnknownReasonWarnDto;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarn;

import java.util.List;

public interface TbCdcmrUnknownReasonWarnMapper {

    void insert(TbCdcmrUnknownReasonWarn tbCdcmrUnknownReasonWarn);

    TbCdcmrUnknownReasonWarn getByDiseaseCode(String diseasesCode);

    void deleteById(String id);

    void updateByPrimaryKeySelective(TbCdcmrUnknownReasonWarn tbCdcmrUnknownReasonWarn);

    List<UnknownReasonWarnDto> getList(UnknownReasonQueryDto dto);

    void updateStatusByPrimaryKey(TbCdcmrUnknownReasonWarn tbCdcmrUnknownReasonWarn);

    TbCdcmrUnknownReasonWarn selectByPrimaryKey(String id);

    List<UnknownReasonWarnDto> getAllList(String diseaseCode);
}