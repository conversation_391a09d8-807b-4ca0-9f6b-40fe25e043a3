package com.iflytek.cdc.admin.controller.province;



import com.iflytek.cdc.admin.dto.ReportRemindTimeGapConfigEditDTO;
import com.iflytek.cdc.admin.entity.ReportRemindTimeGapConfig;
import com.iflytek.cdc.admin.service.ReportRemindTimeGapConfigService;
import com.iflytek.cdc.admin.vo.ReportRemindTimeGapConfigVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 报告提醒时间间隔配置
 */
@RestController
@RequestMapping("/pt/{version}/reportRemind/config")
public class ReportRemindTimeGapConfigController {

    private ReportRemindTimeGapConfigService reportRemindTimeGapConfigService;

    @Autowired
    public void setReportRemindTimeGapConfigService(ReportRemindTimeGapConfigService reportRemindTimeGapConfigService) {
        this.reportRemindTimeGapConfigService = reportRemindTimeGapConfigService;
    }

    /**
     * 根据orgId查询报告提醒时间间隔配置
     * @return
     */
    @GetMapping("/queryByOrgId")
    public ReportRemindTimeGapConfig queryByOrgId(@RequestParam String orgId,
                                                  @RequestParam String loginUserId){
        return this.reportRemindTimeGapConfigService.queryByOrgId(orgId,loginUserId);
    }

    /**
     * 根据id修改报告提醒时间间隔配置
     * @return
     */
    @PostMapping("/editById")
    public ReportRemindTimeGapConfig editById(@RequestBody ReportRemindTimeGapConfigEditDTO dto,
                                              @RequestParam String loginUserId){
        return this.reportRemindTimeGapConfigService.editById(dto,loginUserId);
    }

    /**
     * 根据loginUserId查询报告提醒时间间隔配置
     * @param loginUserId
     * @return
     */
    @GetMapping("/queryByLoginUserId")
    public ReportRemindTimeGapConfigVO queryByLoginUserId(@RequestParam String loginUserId){
        return this.reportRemindTimeGapConfigService.queryByLoginUserId(loginUserId);
    }
}
