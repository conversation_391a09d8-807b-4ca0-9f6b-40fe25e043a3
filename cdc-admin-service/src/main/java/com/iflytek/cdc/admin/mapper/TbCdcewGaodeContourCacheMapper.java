package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.TbCdcewGaodeContourCache;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
public interface TbCdcewGaodeContourCacheMapper extends BaseMapper<TbCdcewGaodeContourCache> {

    TbCdcewGaodeContourCache selectOneByCondition(@Param("key") String key,
                                                  @Param("keywords") String keywords,
                                                  @Param("subdistrict") String subdistrict,
                                                  @Param("extensions") String extensions);
}
