package com.iflytek.cdc.admin.mapper;


import com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRecord;

public interface TbCdcmrPoisonSmsRecordMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrPoisonSmsRecord record);

    int insertSelective(TbCdcmrPoisonSmsRecord record);

    TbCdcmrPoisonSmsRecord selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrPoisonSmsRecord record);

    int updateByPrimaryKey(TbCdcmrPoisonSmsRecord record);
}