package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.entity.TbCdcmrCommonParam;
import com.iflytek.cdc.admin.service.province.TbCdcmrCommonParamService;
import com.iflytek.cdc.admin.dto.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "通用配置参数管理-PB版本")
@RestController
@RequestMapping("/pb/{version}/province/common/param")
public class PbCommonParamController {

    @Resource
    private TbCdcmrCommonParamService tbCdcmrCommonParamService;

    @ApiOperation("根据paramKey获取配置详情")
    @GetMapping("/key")
    public ResponseResult<TbCdcmrCommonParam> getByParamKey(@PathVariable String version, @RequestParam String paramKey) {
        return new ResponseResult<>(tbCdcmrCommonParamService.getByParamKey(paramKey));
    }
} 