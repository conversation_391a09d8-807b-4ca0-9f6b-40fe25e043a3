package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.UnknownReasonGradeConfigVO;
import com.iflytek.cdc.admin.dto.UnknownReasonQueryDto;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrUnknownReasonDiseaseMapper {

    List<TbCdcmrUnknownReasonDisease> getList(UnknownReasonQueryDto dto);

    List<TbCdcmrUnknownReasonDisease> getByDiseaseCode(String diseasesCode);

    List<TbCdcmrUnknownReasonDisease> getByDiseaseName(String diseasesName);

    void insert(TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease);

    TbCdcmrUnknownReasonDisease selectByPrimaryKey(String id);

    void deleteByPrimaryKey(String id);

    void updateByPrimaryKeySelective(TbCdcmrUnknownReasonDisease updateUnknownReasonDisease);

    List<TbCdcmrUnknownReasonDisease> findAll();

    List<UnknownReasonGradeConfigVO> queryUnknownReasonInfoByGrade(@Param("diseaseName") String diseaseName, @Param("status") Integer status);

}