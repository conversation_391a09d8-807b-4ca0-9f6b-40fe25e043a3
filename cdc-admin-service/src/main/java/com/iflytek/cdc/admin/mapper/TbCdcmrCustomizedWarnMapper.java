package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.CustomizedGradeConfigVO;
import com.iflytek.cdc.admin.dto.CustomizedWarnQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarnVO;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedEventTypeConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedLogicField;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrCustomizedWarnMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrCustomizedWarn record);

    int insertSelective(TbCdcmrCustomizedWarn record);

    CustomizedWarnVO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrCustomizedWarn record);

    int updateByPrimaryKey(TbCdcmrCustomizedWarn record);

    List<TbCdcmrCustomizedWarn> getList(CustomizedWarnQueryDTO queryDTO);

    TbCdcmrCustomizedWarn getByName(String name);

    List<CustomizedWarnVO> getEnabledWarn(String waningType);

    List<CustomizedGradeConfigVO> queryCustomizedInfoByGrade(@Param("customizedName") String customizedName, @Param("status") Integer status, @Param("warningType") String warningType);

    /**
     * 根据类型查询自定义配置的逻辑判断字段
     */
    List<TbCdcmrCustomizedLogicField> listLogicFieldBy(@Param("warningType") String warningType,
                                                       @Param("dataSource") String dataSource,
                                                       @Param("groupFlag") Boolean groupFlag);

    /**
     * 根据类型查询自定义配置的聚集性配置
     */
    List<TbCdcmrCustomizedEventTypeConfig> listEventTypeConfigByTypeAndSource(@Param("warningType") String warningType, @Param("dataSource") String dataSource);

}