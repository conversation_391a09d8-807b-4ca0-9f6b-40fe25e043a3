package com.iflytek.cdc.admin.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iflytek.cdc.admin.entity.DiseasePermission;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

@MappedTypes(ArrayList.class)
@MappedJdbcTypes(JdbcType.OTHER)
public class ArrayListJsonTypeHandler extends BaseTypeHandler<List<DiseasePermission.DiseaseInfoData>> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<DiseasePermission.DiseaseInfoData> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            ps.setObject(i, json, Types.OTHER);
        } catch (Exception e) {
            throw new SQLException("Error converting ArrayList to JSON", e);
        }
    }
    @Override
    public List<DiseasePermission.DiseaseInfoData> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJsonArray(json);
    }
    @Override
    public List<DiseasePermission.DiseaseInfoData> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJsonArray(json);
    }
    @Override
    public List<DiseasePermission.DiseaseInfoData> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJsonArray(json);
    }
    private List<DiseasePermission.DiseaseInfoData> parseJsonArray(String json) {
        try {
            if (json == null) {
                return null;
            }
            // 使用 TypeReference 来正确处理泛型类型，避免反序列化为 LinkedHashMap
            return objectMapper.readValue(json, new TypeReference<List<DiseasePermission.DiseaseInfoData>>() {});
        } catch (Exception e) {
            throw new RuntimeException("Error parsing JSON array", e);
        }
    }
}
