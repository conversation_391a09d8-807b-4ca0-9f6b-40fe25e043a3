package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.ExportApplicationListDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationQueryDTO;
import com.iflytek.cdc.admin.dto.ExportApprovalListDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrExportApproval;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TbCdcmrExportApprovalMapper extends BaseMapper<TbCdcmrExportApproval> {

    /**
     * 查询任务关联的申请信息列表（全部，包含自动的）
     *
     * @param dto 过滤条件
     * @return 申请列表
     */
    List<ExportApplicationListDTO> queryTaskApprovals(ExportApplicationQueryDTO dto);

    /**
     * 查询需要人工处理的申请列表（不包含自动的）
     *
     * @param dto 过滤条件
     * @return 申请列表
     */
    List<ExportApprovalListDTO> queryManualApprovalList(ExportApplicationQueryDTO dto);

    /**
     * 自动通过已延期的申请列表
     *
     * @return 影响条数
     */
    int autoPassDelayedApprovals();

    /**
     * 根据任务查询申请
     *
     * @param taskId 任务主键
     * @return 申请记录实体
     */
    @Select("SELECT * FROM tb_cdcmr_export_approval WHERE task_id = #{taskId}")
    TbCdcmrExportApproval selectByTaskId(@Param("taskId") String taskId);
}
