package com.iflytek.cdc.admin.thread;


import com.iflytek.cdc.admin.service.async.AsyncMdmServiceStrategy;

import java.util.concurrent.Callable;

/**
 * @ClassName GenerateAsyncTaskCallable
 * @Description 返回任务状态的执行mdm同步操作
 * <AUTHOR>
 * @Date 2021/8/9 9:54
 * @Version 1.0
 */

public class GenerateAsyncTaskCallable implements Callable<Status> {

    /**
     * redi缓存管理
     */
    public RedisHandlerService redisHandlerService;

    public String batchId;

    public String loginUserId;

    private String code;

    private AsyncMdmServiceStrategy asyncMdmServiceStrategy;

    public GenerateAsyncTaskCallable(String code, String batchId, String loginUserId, RedisHandlerService redisHandlerService, AsyncMdmServiceStrategy asyncMdmServiceStrategy) {
        this.code = code;
        this.batchId = batchId;
        this.loginUserId = loginUserId;
        this.redisHandlerService = redisHandlerService;
        this.asyncMdmServiceStrategy = asyncMdmServiceStrategy;
    }

    @Override
    public Status call() throws Exception {
        redisHandlerService.setStatus(batchId, Status.DOING);
        asyncMdmServiceStrategy.executeAsyncMdmData(code,batchId, loginUserId);
        return Status.DONE;
    }

    public String getBatchId() {
        return batchId;
    }

    public String getCode() {
        return code;
    }
}
