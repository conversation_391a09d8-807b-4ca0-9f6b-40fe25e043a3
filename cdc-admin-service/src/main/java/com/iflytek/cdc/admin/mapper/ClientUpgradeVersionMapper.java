package com.iflytek.cdc.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.ClientUpgradeParamDto;
import com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto;
import com.iflytek.cdc.admin.entity.ClientUpgradeVersion;
import com.iflytek.cdc.admin.sdk.entity.ClientUpgradeFilter;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * @ClassName ClientUpgradeVersionMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/2 15:22
 * @Version 1.0
 */
public interface ClientUpgradeVersionMapper extends BaseMapper<ClientUpgradeVersion> {

    List<ClientUpgradeVersionDto> queryClientUpgradeDtoByVersionCode(ClientUpgradeParamDto clientUpgradeParamDto);

    List<ClientUpgradeVersionDto> queryClientUpgradeDtoById(ClientUpgradeParamDto clientUpgradeParamDto);

    List<ClientUpgradeVersionDto> queryClientUpgradeVersionCountDto(ClientUpgradeVersionDto clientUpgradeVersionDto);

    List<ClientUpgradeVersionDto> queryCurrentVersionByOrgId(ClientUpgradeParamDto clientUpgradeParamDto );

    List<ClientUpgradeVersionDto> queryPreVersionByOrgId(ClientUpgradeParamDto clientUpgradeParamDto );

    List<ClientUpgradeVersionDto> queryVersionCountByOrgId(ClientUpgradeParamDto clientUpgradeParamDto);

    List<ClientUpgradeVersionDto> queryClientUpgradeVersionDto(ClientUpgradeParamDto clientUpgradeParamDto);

    int updateClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDto);

    int addClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDto);

    int addBatchClientUpgradeVersionDto( @Param("clientUpgradeVersionDtos") List<ClientUpgradeVersionDto> clientUpgradeVersionDtos);

    int updateBatchClientUpgradeVersionDto( @Param("clientUpgradeVersionDtos") List<ClientUpgradeVersionDto> clientUpgradeVersionDtos);

    int updateClientIsUpgradeVersion(String[] orgCodes);

    int deleteClientUpgradeVersion(ClientUpgradeParamDto clientUpgradeParamDto);

    int updateClientUpgradeVersionIsDelete(ClientUpgradeParamDto clientUpgradeParamDto);

    /**
     *  删除参数配置信息
     * @param id
     * @param updateUser
     * @param isDelete
     * @return
     **/
    void deleteVersionById(@Param("id") String id,@Param("updateUser") String updateUser,@Param("isDelete") String isDelete);

    List<ClientUpgradeVersion> getAllClientVersion(@Param("param")ClientUpgradeFilter clientUpgradeFilter);

    ClientUpgradeVersion getParamClientVersion(@Param("param")ClientUpgradeFilter clientUpgradeFilter);
}