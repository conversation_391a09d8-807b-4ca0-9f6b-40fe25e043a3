package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.BusinessPermission;
import com.iflytek.cdc.admin.entity.DiseasePermission;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DiseasePermissionMapper extends BaseMapper<DiseasePermission> {
    DiseasePermission queryByOrgIdAndBizSystemCode(String orgId, String subSystemCode);

    List<DiseasePermission> queryListByIdList(List<String> departmentIdList);
}
