package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.WarningGradeQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade;

import java.util.List;

public interface TbCdcmrWarningGradeMapper {
    List<TbCdcmrWarningGrade> getPageList(WarningGradeQueryDTO warningGradeQueryDTO);

    List<TbCdcmrWarningGrade> getList();

    TbCdcmrWarningGrade getByGradeName(String gradeName);

    TbCdcmrWarningGrade getByGradeCode(String gradeCode);

    int deleteByPrimaryKey(String gradeCode);

    int deleteByGradeName(String gradeName);


    int insert(TbCdcmrWarningGrade record);

    int insertSelective(TbCdcmrWarningGrade record);

    int updateByPrimaryKeySelective(TbCdcmrWarningGrade record);

    int updateByPrimaryKey(TbCdcmrWarningGrade record);
}