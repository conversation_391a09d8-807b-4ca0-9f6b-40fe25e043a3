package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.outcall.OutCallResponse;
import com.iflytek.cdc.admin.dto.outcall.SSOutCallContentDto;
import com.iflytek.cdc.admin.entity.TbCdcmrOrgDoctorInfo;
import com.iflytek.cdc.admin.mapper.TbCdcmrOrgDoctorInfoMapper;
import com.iflytek.fpva.constants.OutCallConstants;
import com.iflytek.fpva.dto.OutCallParam;
import com.iflytek.fpva.dto.OutCallPerson;
import com.iflytek.fpva.dto.OutCallResult;
import com.iflytek.fpva.service.OutCallSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR> Lee
 * @Date 2022/4/21
 */
@Slf4j
@Service
public class OutCallService {

    @Autowired(required = false)
    private OutCallSmsService smsService;

    @Autowired
    private TbCdcmrOrgDoctorInfoMapper orgDoctorInfoMapper;

    public OutCallResponse ssOutCallSms(SSOutCallContentDto dto) {
        OutCallResponse response = new OutCallResponse();
        try {
            // 外呼对象
            List<TbCdcmrOrgDoctorInfo> doctorInfoList = orgDoctorInfoMapper.selectAllByOrgId(dto.getOrgId());
            ArrayList<OutCallPerson> list = new ArrayList<>();
            doctorInfoList.stream().forEach(doc -> {
                OutCallPerson person = new OutCallPerson();
                person.setName(doc.getDocName());
                person.setPhone(doc.getPhone());
                list.add(person);
            });
            // 外呼内容
            String template = Constants.SS_OUTCALL_TEMPLATE;
            String[] contents = new String[]{dto.getOrgName(), dto.getSource(), dto.getDeptName(), dto.getPatientName(), dto.getParent(),
                    dto.getIdCardNum(), dto.getSex(), dto.getBirthday(), dto.getAge(), dto.getCompany(), dto.getPhone(), dto.getAddress(),
                    dto.getJob(), dto.getCaseType(), dto.getOnsetDate(), dto.getDiagDate(), dto.getDiagDetail(), dto.getReportDoc(), dto.getReportDate(), dto.getRemark()};
            for (String s : contents) {
                template = StrUtil.format(template, s);
            }
            OutCallParam param = new OutCallParam();
            param.setPersonList(list);
            param.setContent(template);
            param.setCenter(dto.getOrgName());
            OutCallResult result = smsService.outCallSms(param);
            log.info("外呼调用结果：{}", result);
            if (NumberUtil.equals(result.getCode(), HttpStatus.HTTP_OK)) {
                response.setCode(HttpStatus.HTTP_OK);
                response.setMessage(OutCallConstants.OUT_CALL_SUCCESS);
                return response;
            }
        } catch (Exception e) {
            log.error("外呼失败", e);
        }
        response.setCode(HttpStatus.HTTP_UNAVAILABLE);
        response.setMessage(OutCallConstants.OUT_CALL_FAIL);
        return response;
    }
}
