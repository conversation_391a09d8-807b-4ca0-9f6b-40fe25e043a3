package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrWarningGradeConfigMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrWarningGradeConfig record);

    int insertSelective(TbCdcmrWarningGradeConfig record);

    TbCdcmrWarningGradeConfig selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrWarningGradeConfig record);

    int updateByPrimaryKey(TbCdcmrWarningGradeConfig record);

    int upsertConfig(TbCdcmrWarningGradeConfig config);

    List<TbCdcmrWarningGradeConfig> getList();

    List<TbCdcmrWarningGradeConfig> getListByConfigType(String configType);

    TbCdcmrWarningGradeConfig getListByConfigTypeAndCode(@Param("configType") String configType, @Param("diseaseCode") String diseaseCode);


}