package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTaskArea;

import java.util.List;

public interface TbCdcmrCustomizedWarningTaskAreaMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrCustomizedWarningTaskArea record);

    int insertSelective(TbCdcmrCustomizedWarningTaskArea record);

    TbCdcmrCustomizedWarningTaskArea selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrCustomizedWarningTaskArea record);

    int updateByPrimaryKey(TbCdcmrCustomizedWarningTaskArea record);

    int deleteByTaskId(String taskId);

    int batchInsert(List<TbCdcmrCustomizedWarningTaskArea> recordList);

    List<TbCdcmrCustomizedWarningTaskArea> selectByTaskId(String taskId);

}