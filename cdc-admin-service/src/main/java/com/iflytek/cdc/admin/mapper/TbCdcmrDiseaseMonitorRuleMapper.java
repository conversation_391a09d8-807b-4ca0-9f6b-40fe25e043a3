package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.TbCdcmrDiseaseMonitorRule;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface TbCdcmrDiseaseMonitorRuleMapper extends BaseMapper<TbCdcmrDiseaseMonitorRule> {

    List<TbCdcmrDiseaseMonitorRule> getMonitorConfigMsg(@Param("diseaseCode") String diseaseCode,@Param("diseaseName") String diseaseName,
                                                        @Param("configType") String configType);

    void deleteByLogic(@Param("id") String id,
                       @Param("loginUserId") String loginUserId,
                       @Param("timeNow") LocalDateTime timeNow);
}
