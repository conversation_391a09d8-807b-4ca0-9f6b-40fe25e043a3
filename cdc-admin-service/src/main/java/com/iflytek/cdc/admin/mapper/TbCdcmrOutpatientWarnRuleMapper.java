package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.OutpatientWarnRuleExportDataDto;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrOutpatientWarnRuleMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrOutpatientWarnRule record);

    int insertSelective(TbCdcmrOutpatientWarnRule record);

    TbCdcmrOutpatientWarnRule selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrOutpatientWarnRule record);

    int updateByPrimaryKey(TbCdcmrOutpatientWarnRule record);

    List<TbCdcmrOutpatientWarnRule> getRuleListByWarnId(String warnId);

    List<TbCdcmrOutpatientWarnRule> getAllRuleList();

    List<OutpatientWarnRuleExportDataDto> getExportData();

    int deleteOtherByIds(@Param("idList") List<String> idList, @Param("warnId") String warnId);

    int upsertRules(List<TbCdcmrOutpatientWarnRule> recordList);
}