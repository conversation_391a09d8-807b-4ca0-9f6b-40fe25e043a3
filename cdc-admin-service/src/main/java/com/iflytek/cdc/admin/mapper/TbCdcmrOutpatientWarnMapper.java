package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.OutpatientQueryDTO;
import com.iflytek.cdc.admin.dto.OutpatientWarnDto;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn;
import java.util.List;

public interface TbCdcmrOutpatientWarnMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrOutpatientWarn record);

    int insertSelective(TbCdcmrOutpatientWarn record);

    TbCdcmrOutpatientWarn selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrOutpatientWarn record);

    int updateByPrimaryKey(TbCdcmrOutpatientWarn record);

    TbCdcmrOutpatientWarn getByOutpatientTypeCode(String outpatientTypeCode);

    TbCdcmrOutpatientWarn getByOutpatientTypeName(String outpatientTypeName);

    List<OutpatientWarnDto> getList(OutpatientQueryDTO queryDTO);

    List<OutpatientWarnDto> getAllList(String outpatientCode);

    int updateStatusByPrimaryKey(TbCdcmrOutpatientWarn record);

}