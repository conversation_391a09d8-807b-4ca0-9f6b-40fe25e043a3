package com.iflytek.cdc.admin.thread;


import com.iflytek.cdc.admin.service.async.AsyncMdmServiceStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.*;

/**
 * @ClassName MdmSyncThreadPool
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/9 9:46
 * @Version 1.0
 */

@Component
@Slf4j
public class MdmSyncThreadPool {

    @Resource
    RedisHandlerService redisHandlerService;

    @Resource
    AsyncMdmServiceStrategy asyncMdmServiceStrategy;

    /**
     * 线程池最大容量
     */
    public static final int MAX_POOL = 5;

    private  static  BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<Runnable>(2);

    /**
     * 线程池
     */
    public static  ExecutorService pools = new ThreadPoolExecutor(3,
            6,
            2,
            TimeUnit.SECONDS,
            workQueue,
            new ThreadPoolExecutor.AbortPolicy());;

    public void executeMdmSync(String code, String batchId, String loginUserId) {
        redisHandlerService.setStatus(batchId, Status.TODO);
        GenerateAsyncTaskCallable taskCallable = new GenerateAsyncTaskCallable(code, batchId, loginUserId, redisHandlerService, asyncMdmServiceStrategy);
        Future<Status> future = pools.submit(taskCallable);
        log.warn("MdmSyncThreadPool execute thread ,batchId={}", batchId);
        pools.execute(new GenerateStatusThread(future, taskCallable, redisHandlerService));
    }
}
