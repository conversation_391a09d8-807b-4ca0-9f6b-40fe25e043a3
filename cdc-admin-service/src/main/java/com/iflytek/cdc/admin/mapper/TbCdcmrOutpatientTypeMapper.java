package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.OutpatientGradeConfigVO;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrOutpatientTypeMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrOutpatientType record);

    int insertSelective(TbCdcmrOutpatientType record);

    TbCdcmrOutpatientType selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrOutpatientType record);

    int updateByPrimaryKey(TbCdcmrOutpatientType record);

    List<TbCdcmrOutpatientType> getList();

    TbCdcmrOutpatientType getByOutpatientTypeCode(String outpatientTypeCode);

    TbCdcmrOutpatientType getByOutpatientTypeName(String outpatientTypeName);

    List<TbCdcmrOutpatientType> findAll();

    List<OutpatientGradeConfigVO> queryOutpatientInfoByGrade(@Param("outpatientTypeCode") String outpatientTypeCode, @Param("status") Integer status);

}