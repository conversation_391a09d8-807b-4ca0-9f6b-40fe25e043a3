package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.InspectionMappingPageVO;
import com.iflytek.cdc.admin.dto.InspectionMappingVO;
import com.iflytek.cdc.admin.entity.TbCdcmrInspectionMapping;

import java.util.List;

public interface TbCdcmrInspectionMappingMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrInspectionMapping record);

    int insertSelective(TbCdcmrInspectionMapping record);

    TbCdcmrInspectionMapping selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrInspectionMapping record);

    int updateByPrimaryKey(TbCdcmrInspectionMapping record);

    int batchInsert(List<InspectionMappingVO> RecordList);

    int deleteByInfectedCode(String infectedCode);

    List<InspectionMappingVO> getList(String infectedCode);

    List<InspectionMappingPageVO> pageList(String diseaseCode, String diseaseType);

    List<InspectionMappingPageVO> subPageList(String diseaseCode, String diseaseType);

}