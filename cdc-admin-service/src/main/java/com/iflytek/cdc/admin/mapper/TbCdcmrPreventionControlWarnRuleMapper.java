package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.PreventionControlWarnRuleExportDataDto;
import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlWarnRule;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

public interface TbCdcmrPreventionControlWarnRuleMapper {

    List<TbCdcmrPreventionControlWarnRule> getRuleListByWarnId(@Param("warnId") String warnId);

    void deleteOtherByIds(List<String> idList, String warnId);

    void upsertRules(List<TbCdcmrPreventionControlWarnRule> recordList);

    List<TbCdcmrPreventionControlWarnRule> getAllRuleList();

    List<PreventionControlWarnRuleExportDataDto> getExportData();
}