package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.PoisoningQueryDTO;
import com.iflytek.cdc.admin.dto.PoisoningWarnDto;
import com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarn;

import java.util.List;

public interface TbCdcmrPoisoningWarnMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrPoisoningWarn record);

    TbCdcmrPoisoningWarn selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrPoisoningWarn record);

    int updateByPoisoning(TbCdcmrPoisoningWarn record);

    int updateStatusByPrimaryKey(TbCdcmrPoisoningWarn record);

    TbCdcmrPoisoningWarn getByPoisoningCode(String poisoningCode);

    TbCdcmrPoisoningWarn getByPoisoningName(String poisoningName);

    List<PoisoningWarnDto> getList(PoisoningQueryDTO queryDTO);

    List<PoisoningWarnDto> getAllList(String poisoningCode);


}