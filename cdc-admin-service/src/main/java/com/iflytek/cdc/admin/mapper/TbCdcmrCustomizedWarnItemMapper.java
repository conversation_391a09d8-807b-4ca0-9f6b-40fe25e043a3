package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarnItem;

import java.util.List;

public interface TbCdcmrCustomizedWarnItemMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrCustomizedWarnItem record);

    int insertSelective(TbCdcmrCustomizedWarnItem record);

    TbCdcmrCustomizedWarnItem selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrCustomizedWarnItem record);

    int updateByPrimaryKey(TbCdcmrCustomizedWarnItem record);

    int batchInsert(List<TbCdcmrCustomizedWarnItem> recordList);

    int deleteByWarnId(String warnId);

    List<TbCdcmrCustomizedWarnItem> getByWarnId(String warnId);
}