package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule;

import java.util.List;

public interface TbCdcmrInspectionRuleMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrInspectionRule record);

    int insertSelective(TbCdcmrInspectionRule record);

    TbCdcmrInspectionRule selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrInspectionRule record);

    int updateByPrimaryKey(TbCdcmrInspectionRule record);

    int batchInsert(List<TbCdcmrInspectionRule> RecordList);

    int deleteByMappingId(String mappingId);

    List<TbCdcmrInspectionRule> getList(String mappingId);

    int deleteByInfectedCode(String diseaseCode);

}