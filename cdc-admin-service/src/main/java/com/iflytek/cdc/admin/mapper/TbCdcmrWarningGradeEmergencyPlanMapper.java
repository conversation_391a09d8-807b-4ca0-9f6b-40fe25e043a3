package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrWarningGradeEmergencyPlanMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrWarningGradeEmergencyPlan record);

    int insertSelective(TbCdcmrWarningGradeEmergencyPlan record);

    TbCdcmrWarningGradeEmergencyPlan selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrWarningGradeEmergencyPlan record);

    int updateByPrimaryKey(TbCdcmrWarningGradeEmergencyPlan record);

    List<TbCdcmrWarningGradeEmergencyPlan> getByConfigId(String configId);

    int deleterByConfigId(@Param("configId") String configId);

    int insertPlans(List<TbCdcmrWarningGradeEmergencyPlan> recordList);

    List<String> getAttachmentIds(@Param("configType") String configType, @Param("diseaseCode") String diseaseCode);
}