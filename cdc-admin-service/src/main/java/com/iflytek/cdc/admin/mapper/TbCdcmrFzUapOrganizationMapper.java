package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrFzUapOrganization;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TbCdcmrFzUapOrganizationMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrFzUapOrganization record);

    int insertSelective(TbCdcmrFzUapOrganization record);

    TbCdcmrFzUapOrganization selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrFzUapOrganization record);

    int updateByPrimaryKey(TbCdcmrFzUapOrganization record);
}