package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrUserDataAuthMapper {
    List<TbCdcmrUserDataAuth> findByLoginUserId(String loginUserId);

    void batchInsert(@Param("list") List<TbCdcmrUserDataAuth> personDataAuthList);

    void deleteByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> findByLoginUserIds(@Param("loginUserIds")List<String> loginUserIds);

    List<TbCdcmrUserDataAuth> getDataAuthByLoginUserIdAndDataType(@Param("loginUserId") String loginUserId,@Param("dataType") Integer dataType);
}