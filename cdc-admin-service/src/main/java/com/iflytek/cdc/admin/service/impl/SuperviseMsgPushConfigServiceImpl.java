package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgPo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordDto;
import com.iflytek.cdc.admin.dto.SuperviseMsgPushConfigQueryDTO;
import com.iflytek.cdc.admin.entity.SuperviseMsgPushConfig;
import com.iflytek.cdc.admin.mapper.SuperviseMsgPushConfigMapper;
import com.iflytek.cdc.admin.service.SuperviseMsgPushConfigService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SuperviseMsgPushConfigServiceImpl extends ServiceImpl<SuperviseMsgPushConfigMapper, SuperviseMsgPushConfig>
        implements SuperviseMsgPushConfigService {

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }



    @Override
    public SuperviseMsgPushConfig create(SuperviseMsgPushConfig config, String loginUserId) {


        checkData(config);

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        UapOrgPo userOrg = uapServiceApi.getUserOrg(user.getLoginName());
        if (userOrg != null){
            String province = userOrg.getProvince();
            if (StrUtil.isNotBlank(province)){
                config.setProvinceName(province);
                config.setProvinceCode(userOrg.getProvinceCode());
            }

            String city = userOrg.getCity();
            if (StrUtil.isNotBlank(city)){
                config.setCityName(city);
                config.setCityCode(userOrg.getCityCode());
            }

            String district = userOrg.getDistrict();
            if (StrUtil.isNotBlank(district)){
                config.setDistrictName(district);
                config.setDistrictCode(userOrg.getDistrictCode());
            }
        }


        config.setId(String.valueOf(batchUidService.getUid(SuperviseMsgPushConfig.TB_NAME)));

        config.setCreatorId(user.getId());
        config.setCreator(user.getName());
        config.setCreateTime(new Date());

        config.setUpdaterId(user.getId());
        config.setUpdater(user.getName());
        config.setUpdateTime(new Date());

        // 查询是否存在
        LambdaQueryWrapper<SuperviseMsgPushConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SuperviseMsgPushConfig::getMsgType,config.getMsgType())
                .eq(SuperviseMsgPushConfig::getDiseaseClassCode,config.getDiseaseClassCode())
                .eq(SuperviseMsgPushConfig::getProvinceCode,config.getProvinceCode())
                .last( " limit 1 ");
        SuperviseMsgPushConfig one = this.getOne(wrapper);
        if (one != null){
            throw new MedicalBusinessException("该消息提醒类型配置已存在");
        }
        this.save(config);
        return config;
    }

    private void checkData(SuperviseMsgPushConfig config) {
        Integer msgType = config.getMsgType();
        if (msgType == null){
            throw new MedicalBusinessException("消息提醒类型必填");
        }
        if (!(msgType == 1 || msgType == 2)){
            throw new MedicalBusinessException("消息提醒类型错误");
        }
        Integer diseaseClassCode = config.getDiseaseClassCode();
        if (diseaseClassCode == null){
            throw new MedicalBusinessException("传染病类型必填");
        }

        if (! (diseaseClassCode == 1 || diseaseClassCode == 2 || diseaseClassCode == 3)){
            throw new MedicalBusinessException("传染病类型错误");
        }

        String diseaseClassName = config.getDiseaseClassName();
        if (StrUtil.isBlank(diseaseClassName)){
            throw new MedicalBusinessException("传染病类型名称必填");
        }

        Integer remindInterval = config.getRemindInterval();
        if (remindInterval == null){
            throw new MedicalBusinessException("提醒间隔时长必填");
        }
        if (remindInterval < 0 || remindInterval > 24){
            throw new MedicalBusinessException("提醒间隔时长最大为24小时, 最小为0");
        }

        Integer remindCount = config.getRemindCount();

        if (remindCount == null){
            throw new MedicalBusinessException("提醒次数必填");
        }

        if (remindCount < 0 || remindCount > 10){
            throw new MedicalBusinessException("提醒次数最大为10次, 最小为0");
        }
        Integer pushMethod = config.getPushMethod();
        if (pushMethod == null){
            throw new MedicalBusinessException("推送方式必填");
        }

        Integer status = config.getStatus();
        if (status == null){
            throw new MedicalBusinessException("状态必填");
        }
        if (!(status == 0 || status == 1)){
            throw new MedicalBusinessException("状态错误");
        }
    }

    @Override
    public SuperviseMsgPushConfig edit(SuperviseMsgPushConfig config, String loginUserId) {
        if (config == null){
            throw new MedicalBusinessException("缺少参数");
        }
        String id = config.getId();
        if (StrUtil.isBlank(id)){
            throw new MedicalBusinessException("缺少参数");
        }
        SuperviseMsgPushConfig superviseMsgPushConfig = this.getById(id);
        if (superviseMsgPushConfig == null){
            throw new MedicalBusinessException("未查询到该配置");
        }
        Integer remindInterval = config.getRemindInterval();
        if (remindInterval == null){
            throw new MedicalBusinessException("提醒间隔时长必填");
        }
        if (remindInterval < 0 || remindInterval > 24){
            throw new MedicalBusinessException("提醒间隔时长最大为24小时, 最小为0");
        }

        Integer remindCount = config.getRemindCount();

        if (remindCount == null){
            throw new MedicalBusinessException("提醒次数必填");
        }

        if (remindCount < 0 || remindCount > 10){
            throw new MedicalBusinessException("提醒次数最大为10次, 最小为0");
        }
        Integer status = config.getStatus();
        if (status == null){
            throw new MedicalBusinessException("状态必填");
        }
        if (!(status == 0 || status == 1)){
            throw new MedicalBusinessException("状态错误");
        }

        superviseMsgPushConfig.setStatus(status);
        superviseMsgPushConfig.setRemindCount(remindCount);
        superviseMsgPushConfig.setRemindInterval(remindInterval);

        UapUserPo user = uapServiceApi.getUser(loginUserId);

        superviseMsgPushConfig.setUpdater(user.getName());
        superviseMsgPushConfig.setUpdaterId(user.getId());
        superviseMsgPushConfig.setUpdateTime(new Date());

        this.updateById(superviseMsgPushConfig);
        return superviseMsgPushConfig;
    }

    @Override
    public Boolean delete(String id, String loginUserId) {
        if (StrUtil.isBlank(id)){
            throw new MedicalBusinessException("缺少参数");
        }
        SuperviseMsgPushConfig superviseMsgPushConfig = this.getById(id);
        if (superviseMsgPushConfig == null){
            throw new MedicalBusinessException("未查询到该配置");
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        superviseMsgPushConfig.setDeleteFlag(1);
        superviseMsgPushConfig.setUpdateTime(new Date());
        superviseMsgPushConfig.setUpdater(user.getName());
        superviseMsgPushConfig.setUpdaterId(user.getId());

        return this.updateById(superviseMsgPushConfig);
    }

    @Override
    public PageInfo<SuperviseMsgPushConfig> queryList(SuperviseMsgPushConfigQueryDTO dto, String loginUserId) {

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());

        LambdaQueryWrapper<SuperviseMsgPushConfig> wrapper = new LambdaQueryWrapper<>();

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        UapOrgPo userOrg = uapServiceApi.getUserOrg(user.getLoginName());

        if (userOrg != null){
            wrapper.eq(SuperviseMsgPushConfig::getProvinceCode,userOrg.getProvinceCode());
        }
        wrapper.eq(SuperviseMsgPushConfig::getDeleteFlag,0);
        if (dto.getMsgType() != null){
            wrapper.eq(SuperviseMsgPushConfig::getMsgType,dto.getMsgType());
        }
        if (dto.getDiseaseClassCode() != null){
            wrapper.eq(SuperviseMsgPushConfig::getDiseaseClassCode,dto.getDiseaseClassCode());
        }
        if (dto.getStartDate() != null){
            wrapper.ge(SuperviseMsgPushConfig::getUpdateTime,dto.getStartDate());
        }
        if (dto.getEndTDate() != null){
            wrapper.le(SuperviseMsgPushConfig::getUpdateTime,dto.getEndTDate());
        }
        List<SuperviseMsgPushConfig> list = this.list(wrapper);

        return new PageInfo<>(list);
    }

    @Override
    public SuperviseMsgPushConfig detail(String id) {
        if (StrUtil.isBlank(id)){
            throw new MedicalBusinessException("缺少参数");
        }
        LambdaQueryWrapper<SuperviseMsgPushConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SuperviseMsgPushConfig::getId,id)
                .eq(SuperviseMsgPushConfig::getDeleteFlag,0);
        SuperviseMsgPushConfig superviseMsgPushConfig = this.getOne(wrapper);
        if (superviseMsgPushConfig == null){
            throw new MedicalBusinessException("未查询到该配置");
        }
        return superviseMsgPushConfig;
    }

    @Override
    public SuperviseMsgPushConfig queryByDiseaseClassCode(Integer diseaseClassCode,Integer msgType, String loginUserId) {

        if (diseaseClassCode == null){
            return new SuperviseMsgPushConfig();
        }
        LambdaQueryWrapper<SuperviseMsgPushConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SuperviseMsgPushConfig::getDiseaseClassCode,diseaseClassCode)
                .eq(SuperviseMsgPushConfig::getMsgType,msgType)
                .eq(SuperviseMsgPushConfig::getDeleteFlag,0);
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        UapOrgPo userOrg = uapServiceApi.getUserOrg(user.getLoginName());
        if (userOrg != null){
            wrapper.eq(SuperviseMsgPushConfig::getProvinceCode,userOrg.getProvinceCode());
        }
        wrapper.last(" limit 1 ");
        SuperviseMsgPushConfig one = this.getOne(wrapper);
        if (one == null){
            one = new SuperviseMsgPushConfig();
        }

        return one;
    }
}
