package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcmrOutpatientSmsRuleMapper {
    int insert(List<TbCdcmrOutpatientSmsRule> record);

    int updateByPrimaryKeySelective(List<TbCdcmrOutpatientSmsRule> recordList);

    int deleteByOutpatientCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                               @Param("loginUserId") String loginUserId, @Param("outpatientCodeList") List<String> outpatientCodeList);

    List<TbCdcmrOutpatientSmsRule> getListByLoginUserId(@Param("loginUserId") String loginUserId);

    List<TbCdcmrOutpatientSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                            @Param("loginUserId") String loginUserId);

}