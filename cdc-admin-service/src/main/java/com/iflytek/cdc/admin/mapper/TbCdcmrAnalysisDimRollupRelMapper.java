package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimRollupRel;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupRelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrAnalysisDimRollupRelMapper extends BaseMapper<TbCdcmrAnalysisDimRollupRel> {
    void softDelByDimId(String dimId);

    void softDelById(String id);

    List<AnalysisDimRollupRelVO> selectByDimIds(@Param("dimIds") List<String> dimIds);

    /**
     * 获取最大的序号
     */
    Integer loadLastNum(String dimId);
}
