package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrFzcdcOrgMapping;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TbCdcmrFzcdcOrgMappingMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrFzcdcOrgMapping record);

    int insertSelective(TbCdcmrFzcdcOrgMapping record);

    TbCdcmrFzcdcOrgMapping selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrFzcdcOrgMapping record);

    int updateByPrimaryKey(TbCdcmrFzcdcOrgMapping record);
    TbCdcmrFzcdcOrgMapping selectByFzId(String fzOrgId);

    TbCdcmrFzcdcOrgMapping selectByCdcSysId(String oldUapParentId);
}