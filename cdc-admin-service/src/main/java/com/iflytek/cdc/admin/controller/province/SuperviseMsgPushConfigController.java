package com.iflytek.cdc.admin.controller.province;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.SuperviseMsgPushConfigQueryDTO;
import com.iflytek.cdc.admin.entity.SuperviseMsgPushConfig;
import com.iflytek.cdc.admin.service.SuperviseMsgPushConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 督办提醒消息配置
 */
@RestController
@RequestMapping("/pt/{version}/superviseMsgPush/config")
public class SuperviseMsgPushConfigController {

    private SuperviseMsgPushConfigService superviseMsgPushConfigService;

    @Autowired
    public void setSuperviseMsgPushConfigService(SuperviseMsgPushConfigService superviseMsgPushConfigService) {
        this.superviseMsgPushConfigService = superviseMsgPushConfigService;
    }

    /**
     * 新增消息配置
     * @param config
     * @param loginUserId
     * @return
     */
    @PostMapping("/create")
    public SuperviseMsgPushConfig create(@RequestBody SuperviseMsgPushConfig config,
                                         @RequestParam String loginUserId){
        return superviseMsgPushConfigService.create(config,loginUserId);
    }

    /**
     * 编辑消息配置
     * @param config
     * @param loginUserId
     * @return
     */
    @PostMapping("/edit")
    public SuperviseMsgPushConfig edit(@RequestBody SuperviseMsgPushConfig config,
                                       @RequestParam String loginUserId){
        return superviseMsgPushConfigService.edit(config,loginUserId);
    }

    /**
     * 删除消息配置
     */
    @PostMapping("/delete")
    public Boolean delete(@RequestParam String id,
                          @RequestParam String loginUserId){
        return superviseMsgPushConfigService.delete(id,loginUserId);
    }

    /**
     * 列表查询
     */
    @PostMapping("/list")
    public PageInfo<SuperviseMsgPushConfig> queryList(@RequestBody SuperviseMsgPushConfigQueryDTO dto,
                                                 @RequestParam String loginUserId){
        return superviseMsgPushConfigService.queryList(dto,loginUserId);
    }

    /**
     * 查询详情
     */
    @PostMapping("/detail")
    public SuperviseMsgPushConfig detail(@RequestParam String id){
        return superviseMsgPushConfigService.detail(id);
    }

    /**
     * 通过传染病类型和消息类型查询
     * @param diseaseClassCode
     * @param msgType
     * @param loginUserId
     * @return
     */
    @GetMapping("/queryByDiseaseClassCode")
    public SuperviseMsgPushConfig queryByDiseaseClassCode(@RequestParam Integer diseaseClassCode,
                                         @RequestParam Integer msgType,
                                         @RequestParam String loginUserId){
        return superviseMsgPushConfigService.queryByDiseaseClassCode(diseaseClassCode,msgType,loginUserId);
    }
}
