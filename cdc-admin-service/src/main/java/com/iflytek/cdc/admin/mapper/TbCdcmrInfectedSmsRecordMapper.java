package com.iflytek.cdc.admin.mapper;


import com.iflytek.cdc.admin.dto.TbCdcmrInfectedSmsRecord;

public interface TbCdcmrInfectedSmsRecordMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcmrInfectedSmsRecord record);

    int insertSelective(TbCdcmrInfectedSmsRecord record);

    TbCdcmrInfectedSmsRecord selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrInfectedSmsRecord record);

    int updateByPrimaryKey(TbCdcmrInfectedSmsRecord record);
}