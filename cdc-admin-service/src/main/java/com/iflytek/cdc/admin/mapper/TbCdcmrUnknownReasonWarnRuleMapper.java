package com.iflytek.cdc.admin.mapper;

import com.fasterxml.jackson.databind.JsonSerializable;
import com.iflytek.cdc.admin.dto.UnknownReasonWarnRuleExportDataDto;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarnRule;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

public interface TbCdcmrUnknownReasonWarnRuleMapper {

    List<TbCdcmrUnknownReasonWarnRule> getRuleListByWarnId(@Param("warnId") String warnId);

    void deleteOtherByIds(List<String> idList, String warnId);

    void upsertRules(List<TbCdcmrUnknownReasonWarnRule> recordList);

    List<TbCdcmrUnknownReasonWarnRule> getAllRuleList();

    List<UnknownReasonWarnRuleExportDataDto> getExportData();
}