package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.dto.ReportRemindTimeGapConfigEditDTO;
import com.iflytek.cdc.admin.entity.ReportRemindTimeGapConfig;
import com.iflytek.cdc.admin.vo.ReportRemindTimeGapConfigVO;

public interface ReportRemindTimeGapConfigService extends IService<ReportRemindTimeGapConfig> {
    ReportRemindTimeGapConfig queryByOrgId(String orgId,String loginUserId);

    ReportRemindTimeGapConfig editById(ReportRemindTimeGapConfigEditDTO dto, String loginUserId);

    ReportRemindTimeGapConfigVO queryByLoginUserId(String loginUserId);
}
