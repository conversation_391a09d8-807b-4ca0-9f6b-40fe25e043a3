package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict;

import java.util.List;

public interface TbCdcmrCustomizeDistrictMapper {
    List<TbCdcmrCustomizeDistrict> selectList();

    List<TbCdcmrCustomizeDistrict> selectListWithoutData();

    void deleteCustomizeDistrict(String districtCode);

    int insert(TbCdcmrCustomizeDistrict record);

    TbCdcmrCustomizeDistrict selectByPrimaryKey(String districtCode);

    int updateByPrimaryKeySelective(TbCdcmrCustomizeDistrict record);

}