package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.TbCdcmrSympSmsRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 症状短信配置
 * */
public interface TbCdcmrSympSmsRuleMapper {

    int insert(List<TbCdcmrSympSmsRule> recordList);

    int updateByPrimaryKeySelective(List<TbCdcmrSympSmsRule> recordList);

    int deleteBySymptomCodeList(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                                @Param("loginUserId") String loginUserId, @Param("symptomCodeList") List<String> symptomCodeList);

    List<TbCdcmrSympSmsRule> getListByLoginUserId(String loginUserId);

    List<TbCdcmrSympSmsRule> getListByCodeList(@Param("codeList") List<String> codeList);

    int deleteByLoginUserId(@Param("updater") String updater, @Param("updateTime") Date updateTime,
                            @Param("loginUserId") String loginUserId);
}
