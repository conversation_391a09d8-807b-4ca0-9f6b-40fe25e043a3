package com.iflytek.cdc.admin.controller.province;


import com.iflytek.cdc.admin.dto.BizDiseasePermissionQueryDTO;
import com.iflytek.cdc.admin.dto.BizPermissionQueryDTO;
import com.iflytek.cdc.admin.entity.BusinessPermission;
import com.iflytek.cdc.admin.entity.DiseasePermission;
import com.iflytek.cdc.admin.model.mr.vo.DeptUserPermissionVO;
import com.iflytek.cdc.admin.model.mr.vo.PermissionDiseaseInfoDataVO;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.BusinessProcessPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/pt/{version}/bizProcess/permission")
public class BusinessProcessPermissionController {

    private BusinessProcessPermissionService businessProcessPermissionService;

    @Autowired
    public void setBusinessProcessPermissionService(BusinessProcessPermissionService businessProcessPermissionService) {
        this.businessProcessPermissionService = businessProcessPermissionService;
    }

    /**
     * 获取业务权限
     */
    @PostMapping("/queryBizConfig")
    public BusinessPermission queryBizPermission(@RequestBody BizPermissionQueryDTO dto,
                                                 @RequestParam String loginUserId){
        return businessProcessPermissionService.queryBizPermission(dto,loginUserId);
    }

    /**
     * 更新业务权限
     */
    @PostMapping("/updateBizConfig")
    public BusinessPermission updateBizConfig(@RequestBody BizPermissionQueryDTO dto,
                                                 @RequestParam String loginUserId){
        return businessProcessPermissionService.updateBizConfig(dto,loginUserId);
    }

    /**
     * 获取疾病权限
     */
    @PostMapping("/queryDiseaseConfig")
    public DiseasePermission queryDiseaseConfig(@RequestBody BizDiseasePermissionQueryDTO dto,
                                                @RequestParam String loginUserId){
        return businessProcessPermissionService.queryDiseaseConfig(dto,loginUserId);
    }

    /**
     * 更新疾病权限
     */
    @PostMapping("/updateDiseaseConfig")
    public DiseasePermission updateDiseaseConfig(@RequestBody BizDiseasePermissionQueryDTO dto,
                                                @RequestParam String loginUserId){
        return businessProcessPermissionService.updateDiseaseConfig(dto,loginUserId);
    }

    /**
     * 查询部门用户权限
     */
    @GetMapping("/queryDeptUserPermission")
    public DeptUserPermissionVO queryDeptUserPermission(@RequestParam String subSystemCode,
                                                        @RequestParam String loginUserId){
        return businessProcessPermissionService.queryDeptUserPermission(subSystemCode,loginUserId);
    }

    /**
     * 查询全部疾病code
     */
    @GetMapping("/queryPermissionDiseaseInfoData")
    public PermissionDiseaseInfoDataVO queryPermissionDiseaseInfoData(@RequestParam String subSystemCode,
                                                                      @RequestParam String loginUserId){
        return businessProcessPermissionService.queryPermissionDiseaseInfoData(subSystemCode,loginUserId);
    }

    /**
     * 获取传染病疾病权限树
     */
    @GetMapping("/queryPermissionInfectedInfoData")
    public List<TreeNode> queryPermissionInfectedInfoData(@RequestParam String loginUserId){
        return businessProcessPermissionService.queryPermissionInfectedInfoData(loginUserId);
    }
    /**
     * 获取症候群疾病权限树
     */
    @GetMapping("/queryPermissionSyndromeInfoData")
    public List<SyndromeDiseaseInfoVO> queryPermissionSyndromeInfoData(@RequestParam String loginUserId){
        return businessProcessPermissionService.queryPermissionSyndromeInfoData(loginUserId);
    }
}
