package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcmrFzOrgMapping;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbCdcmrFzOrgMappingMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TbCdcmrFzOrgMapping record);

    int insertSelective(TbCdcmrFzOrgMapping record);

    TbCdcmrFzOrgMapping selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TbCdcmrFzOrgMapping record);

    int updateByPrimaryKey(TbCdcmrFzOrgMapping record);

    List<TbCdcmrFzOrgMapping> selectBySysOrgId(String sysOrgid);
}