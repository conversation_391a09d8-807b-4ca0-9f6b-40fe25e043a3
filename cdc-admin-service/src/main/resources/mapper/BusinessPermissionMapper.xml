<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.BusinessPermissionMapper">

    <resultMap id="base_resultMap" type="com.iflytek.cdc.admin.entity.BusinessPermission">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="subsystem_code" property="subsystemCode"/>
        <result column="business_type_arr" property="businessTypeArr" typeHandler="com.iflytek.cdc.admin.typehandler.ArrayListJsonTypeHandler"/>
    </resultMap>
    <insert id="create">
        insert into app.tb_cdcmr_business_permission (id, org_id, subsystem_code, business_type_arr, create_time, update_time,
        creator_id, creator, updater_id, updater)
        values (#{id},#{orgId},#{subsystemCode},#{businessTypeArr,jdbcType=OTHER}::json
        ,#{createTime},#{updateTime},#{creatorId},#{creator},#{updaterId},#{updater});


    </insert>


    <select id="queryByOrgIdAndBizSystemCode" resultMap="base_resultMap">
        select
        id,
        org_id,
        subsystem_code,
        business_type_arr
        from app.tb_cdcmr_business_permission
        where org_id = #{orgId}
        and subsystem_code = #{subSystemCode}
    </select>
</mapper>