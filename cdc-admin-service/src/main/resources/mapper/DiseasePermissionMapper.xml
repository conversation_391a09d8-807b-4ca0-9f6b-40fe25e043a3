<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.DiseasePermissionMapper">


    <resultMap id="base_resultMap" type="com.iflytek.cdc.admin.entity.DiseasePermission">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="subsystem_code" property="subsystemCode"/>
        <result column="infected_disease_arr" property="infectedDiseaseArr" typeHandler="com.iflytek.cdc.admin.typehandler.ArrayListJsonTypeHandler"/>
        <result column="syndrome_disease_arr" property="syndromeDiseaseArr" typeHandler="com.iflytek.cdc.admin.typehandler.ArrayListJsonTypeHandler"/>
        <result column="infected_all_flag" property="infectedAllFlag"/>
        <result column="syndrome_all_flag" property="syndromeAllFlag"/>
        <result column="infected_disease_info" property="infectedDiseaseInfo"/>
        <result column="syndrome_disease_info" property="syndromeDiseaseInfo"/>
    </resultMap>

    <select id="queryByOrgIdAndBizSystemCode" resultMap="base_resultMap">
        select
        id,
        org_id,
        subsystem_code,
        infected_disease_arr,
        syndrome_disease_arr,
        infected_all_flag,
        syndrome_all_flag,
        infected_disease_info,
        syndrome_disease_info
        from app.tb_cdcmr_disease_permission
        where org_id = #{orgId}
        and subsystem_code = #{subSystemCode}
    </select>
    <select id="queryListByIdList" resultMap="base_resultMap">
        select
        id,
        org_id,
        subsystem_code,
        infected_disease_arr,
        syndrome_disease_arr,
        infected_all_flag,
        syndrome_all_flag
        from app.tb_cdcmr_disease_permission
        where org_id in
        <foreach collection="departmentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>