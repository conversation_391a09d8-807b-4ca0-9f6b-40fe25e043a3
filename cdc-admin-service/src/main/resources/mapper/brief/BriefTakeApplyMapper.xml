<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.cdc.admin.mapper.brief.BriefTakeApplyMapper">

    <resultMap type="com.iflytek.cdc.admin.vo.brief.BriefTakeApplyVo" id="briefTakeApplyVo">
        <result property="id" jdbcType="VARCHAR" column="id"/>
        <result property="applyUser" jdbcType="VARCHAR" column="applyUser"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="templateId" jdbcType="VARCHAR" column="templateId"/>
        <result property="statisticsCycle" jdbcType="VARCHAR" column="statisticsCycle"/>
        <result property="briefReportType" jdbcType="VARCHAR" column="briefReportType"/>
        <result property="templateTitle" jdbcType="VARCHAR" column="templateTitle"/>
        <result property="provinceCode" jdbcType="VARCHAR" column="provinceCode"/>
        <result property="provinceName" jdbcType="VARCHAR" column="provinceName"/>
        <result property="cityCode" jdbcType="VARCHAR" column="cityCode"/>
        <result property="cityName" jdbcType="VARCHAR" column="cityName"/>
        <result property="districtCode" jdbcType="VARCHAR" column="districtCode"/>
        <result property="districtName" jdbcType="VARCHAR" column="districtName"/>
        <result property="applyReason" jdbcType="VARCHAR" column="applyReason"/>
        <result property="approveStatus" jdbcType="INTEGER" column="approveStatus"/>
        <result property="approveMark" jdbcType="VARCHAR" column="approveMark"/>
        <result property="approver" jdbcType="VARCHAR" column="approver"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="approveTime" jdbcType="TIMESTAMP" column="approveTime"/>
        <result property="applyTime" jdbcType="TIMESTAMP" column="applyTime"/>

    </resultMap>


    <select id="queryById" resultMap="briefTakeApplyVo">
        select a.id as id,
        a.apply_user as applyUser,
        a.template_id as templateId,
        t.statistics_cycle as statisticsCycle,
        t.brief_report_type as briefReportType,
        t.title as templateTitle,
        a.apply_reason as applyReason,
        a.approve_status as approveStatus,
        a.approve_mark as approveMark ,
        a.approver as approver,
        a.approve_time as approveTime,
        a.apply_time  as applyTime,
        a.province_code as provinceCode,
        a.province_name as provinceName,
        a.city_code as cityCode,
        a.city_name as cityName,
        a.district_code as districtCode,
        a.district_name as districtName
        from app.tb_cdcbr_brief_take_apply a
        join app.tb_cdcbr_template t on a.template_id = t.id
        where a.id = #{id}
    </select>
    <select id="queryList" resultMap="briefTakeApplyVo">
        select a.id as id,
        a.apply_user as applyUser,
        a.template_id as templateId,
        t.statistics_cycle as statisticsCycle,
        t.brief_report_type as briefReportType,
        t.title as templateTitle,
        a.apply_reason as applyReason,
        a.approve_status as approveStatus,
        a.approve_mark as approveMark ,
        a.approver as approver,
        a.approve_time as approveTime,
        a.apply_time  as applyTime,
        a.province_code as provinceCode,
        a.province_name as provinceName,
        a.city_code as cityCode,
        a.city_name as cityName,
        a.district_code as districtCode,
        a.district_name as districtName
        from app.tb_cdcbr_brief_take_apply a
        join app.tb_cdcbr_template t on a.template_id = t.id
        where 1 = 1
        <if test="id != null and id != '' ">
            and a.id like concat('%', #{id}, '%')
        </if>
        <if test="applyStartDate != null">
            and a.apply_time &gt;= #{applyStartDate}
        </if>
        <if test="applyEndDate != null">
            and a.apply_time &lt;= #{applyStartDate}
        </if>
        <if test="statisticsCycle != null and statisticsCycle != '' ">
            and t.statistics_cycle = #{statisticsCycle}
        </if>
        <if test="briefReportType != null and briefReportType != '' ">
            and t.brief_report_type = #{briefReportType}
        </if>
        <if test="approveStatus != null ">
            and a.approve_status = #{approveStatus}
        </if>
        <if test="approveStartDate != null">
            and a.approve_time &gt;= #{approveStartDate}
        </if>
        <if test="approveEndDate != null">
            and a.approve_time &lt;= #{approveEndDate}
        </if>
        <if test="provinceCode != null and provinceCode != '' ">
            and a.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != '' ">
            and a.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != '' ">
            and a.district_code = #{districtCode}
        </if>
        order by a.apply_time desc
    </select>
    <select id="queryByTemplateId" resultMap="briefTakeApplyVo">
        select a.id as id,
        a.apply_user as applyUser,
        a.template_id as templateId,
        t.statistics_cycle as statisticsCycle,
        t.brief_report_type as briefReportType,
        t.title as templateTitle,
        a.apply_reason as applyReason,
        a.approve_status as approveStatus,
        a.approve_mark as approveMark ,
        a.approver as approver,
        a.approve_time as approveTime,
        a.apply_time  as applyTime,
        a.province_code as provinceCode,
        a.province_name as provinceName,
        a.city_code as cityCode,
        a.city_name as cityName,
        a.district_code as districtCode,
        a.district_name as districtName
        from app.tb_cdcbr_brief_take_apply a
        join app.tb_cdcbr_template t on a.template_id = t.id
        where a.template_id = #{templateId}
        and a.approve_status = 1
    </select>
</mapper>