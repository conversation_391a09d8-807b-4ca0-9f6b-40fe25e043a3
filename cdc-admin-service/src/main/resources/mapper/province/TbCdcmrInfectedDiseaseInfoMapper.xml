<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseInfoMapper">

    <sql id="Base_Column_List">
        id, infected_class_code, infected_class_name, disease_type_code, disease_type_name, parent_disease_id,
        parent_disease_code, parent_disease_name, level_type, disease_code, disease_name, order_flag, transmission_type_code,
        transmission_type_name, management_type_code, management_type_name, alias, notes, status, delete_flag,
        create_time, update_time, creator_id, creator, updater_id, updater
    </sql>

    <insert id="insertBatch">
        insert into tb_cdcmr_infected_disease_info
        (id, infected_class_code, infected_class_name, disease_type_code, disease_type_name, parent_disease_id,
        parent_disease_code, parent_disease_name, level_type, disease_code, disease_name, order_flag, transmission_type_code,
        transmission_type_name, management_type_code, management_type_name, alias, notes, status, delete_flag,
        create_time, update_time, creator_id, creator, updater_id, updater)
        values
        <foreach collection="entities" item="item" separator=",">
            (#{item.id}, #{item.infectedClassCode}, #{item.infectedClassName}, #{item.diseaseTypeCode}, #{item.diseaseTypeName},
            #{item.parentDiseaseId}, #{item.parentDiseaseCode}, #{item.parentDiseaseName},
            #{item.levelType}, #{item.diseaseCode}, #{item.diseaseName},
            #{item.orderFlag}, #{item.transmissionTypeCode}, #{item.transmissionTypeName}, #{item.managementTypeCode}, #{item.managementTypeName},
            #{item.alias}, #{item.notes}, #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.updateTime},
            #{item.creatorId}, #{item.creator}, #{item.updaterId}, #{item.updater})
        </foreach>
    </insert>

    <insert id="insert">
        insert into tb_cdcmr_infected_disease_info
        (id, infected_class_code, infected_class_name, disease_type_code, disease_type_name, parent_disease_id, parent_disease_code,
        parent_disease_name, level_type, disease_code, disease_name, order_flag, transmission_type_code, transmission_type_name,
        management_type_code, management_type_name, alias, notes, status, delete_flag, create_time, update_time,
        creator_id, creator, updater_id, updater)
        values
        (#{id}, #{infectedClassCode}, #{infectedClassName}, #{diseaseTypeCode}, #{diseaseTypeName}, #{parentDiseaseId}, #{parentDiseaseCode},
        #{parentDiseaseName},  #{levelType}, #{diseaseCode}, #{diseaseName}, #{orderFlag}, #{transmissionTypeCode}, #{transmissionTypeName},
        #{managementTypeCode}, #{managementTypeName}, #{alias}, #{notes}, #{status}, #{deleteFlag}, #{createTime}, #{updateTime},
        #{creatorId}, #{creator}, #{updaterId}, #{updater})
    </insert>

    <update id="update">
        update tb_cdcmr_infected_disease_info
        <set>
            <if test="infectedClassCode != null and infectedClassCode != ''">
                infected_class_code = #{infectedClassCode},
            </if>
            <if test="infectedClassName != null and infectedClassName != ''">
                infected_class_name = #{infectedClassName},
            </if>
            <if test="diseaseTypeCode != null and diseaseTypeCode != ''">
                disease_type_code = #{diseaseTypeCode},
            </if>
            <if test="diseaseTypeName != null and diseaseTypeName != ''">
                disease_type_name = #{diseaseTypeName},
            </if>
            <if test="parentDiseaseId != null and parentDiseaseId != ''">
                parent_disease_id = #{parentDiseaseId},
            </if>
            <if test="parentDiseaseCode != null and parentDiseaseCode != ''">
                parent_disease_code = #{parentDiseaseCode},
            </if>
            <if test="parentDiseaseName != null and parentDiseaseName != ''">
                parent_disease_name = #{parentDiseaseName},
            </if>
            <if test="levelType != null and levelType != ''">
                level_type = #{levelType},
            </if>
            <if test="diseaseCode != null and diseaseCode != ''">
                disease_code = #{diseaseCode},
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                disease_name = #{diseaseName},
            </if>
            <if test="orderFlag != null and orderFlag != ''">
                order_flag = #{orderFlag},
            </if>
            <if test="transmissionTypeCode != null and transmissionTypeCode != ''">
                transmission_type_code = #{transmissionTypeCode},
            </if>
            <if test="transmissionTypeName != null and transmissionTypeName != ''">
                transmission_type_name = #{transmissionTypeName},
            </if>
            <if test="managementTypeCode != null and managementTypeCode != ''">
                management_type_code = #{managementTypeCode},
            </if>
            <if test="managementTypeName != null and managementTypeName != ''">
                management_type_name = #{managementTypeName},
            </if>
            <if test="alias != null and alias != ''">
                alias = #{alias},
            </if>
            <if test="notes != null and notes != ''">
                notes = #{notes},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="deleteFlag != null and deleteFlag != ''">
                delete_flag = #{deleteFlag},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="creatorId != null and creatorId != ''">
                creator_id = #{creatorId},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="updaterId != null and updaterId != ''">
                updater_id = #{updaterId},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateSubDiseaseParent">
        update tb_cdcmr_infected_disease_info
        set
        parent_disease_id = #{parentDiseaseId},
        parent_disease_code = #{parentDiseaseCode},
        parent_disease_name = #{parentDiseaseName}
        where parent_disease_id = #{id}
    </update>

    <update id="updateDeleteFlagByIds">
        update tb_cdcmr_infected_disease_info
        set delete_flag = '1'
        where id in
        <foreach collection="ids" item="item" separator="," close=")" open="(" index="index">
            #{item}
        </foreach>
    </update>

    <delete id="deleteAll">
        delete from tb_cdcmr_infected_disease_info;
    </delete>

    <select id="listAll" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
    </select>

    <select id="getAllInfectedInfoBy" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where delete_flag = '0'
        <if test="infectedClassCode != null and infectedClassCode != ''">
            and infected_class_code = #{infectedClassCode}
        </if>
        <if test="infectedTypeCode != null and infectedTypeCode != ''">
            and disease_type_code = #{infectedTypeCode}
        </if>
    </select>

    <select id="getInfectedBaseInfoById"
            resultType="com.iflytek.cdc.admin.model.mr.vo.InfectedMasterDataConfigInfoVO">
        select level_type,
               disease_code        as masterDataCode,
               disease_name        as masterDataName,
               alias               as masterDataAlias,
               parent_disease_id   as parentDataId,
               parent_disease_code as parentDataCode,
               parent_disease_name as parentDataName,
        <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where delete_flag = '0'
        <if test="infectedDiseaseInfoId != null and infectedDiseaseInfoId != ''">
            and id = #{infectedDiseaseInfoId}
        </if>
    </select>

    <select id="getSubInfectedDiseaseInfoByIds"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where 1=1
        <if test="ids != null and ids.size() &gt; 0">
            and parent_disease_id in
            <foreach collection="ids" item="item" separator="," close=")" open="(" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getDiseaseInfoById" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where id = #{id}
    </select>

    <select id="getMaxId" resultType="java.lang.Integer">
        SELECT MAX(CAST(id AS INTEGER)) FROM tb_cdcmr_infected_disease_info
    </select>
    <select id="getAllParentInfectedInfoBy"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where delete_flag = '0'
        <if test="infectedClassCode != null and infectedClassCode != ''">
            and infected_class_code = #{infectedClassCode}
        </if>
        <if test="infectedTypeCode != null and infectedTypeCode != ''">
            and disease_type_code = #{infectedTypeCode}
        </if>
        <if test="parentId != null and parentId != ''">
            and ( parent_disease_id  is null or parent_disease_id=#{parentId})
        </if>

    </select>

    <select id="getDiseaseInfoByCode" resultType="com.iflytek.cdc.admin.model.mr.dto.CommonMasterData">
        select
        id as id,
        id as masterDataCode,
        level_type,
        disease_name as masterDataName,
        parent_disease_id as parentDataId,
        parent_disease_name as parentDataName
        from tb_cdcmr_infected_disease_info
        where delete_flag = '0' and id = #{masterDataCode}
        limit 1
    </select>

    <select id="queryInfecInfo" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where delete_flag = '0' and status = '1'
        <if test="dto.diseasesName != null and dto.diseasesName != ''">
            and disease_name = #{dto.diseasesName}
        </if>
        <if test="dto.diseaseCode != null and dto.diseaseCode != ''">
            and id = #{dto.diseaseCode}
        </if>
    </select>


    <select id="getDiseaseInfoByParentDiseaseId" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where parent_disease_id = #{parentId}
    </select>

    <select id="getInfectedInfoByCodeList" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info
        where delete_flag = '0'
        and id in
        <foreach collection="codeList" item="item" separator="," close=")" open="(" index="index">
            #{item}
        </foreach>
    </select>
</mapper>