package com.iflytek.cdc.admin.evaluation.builder;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnit;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnitItem;
import com.iflytek.cdc.admin.evaluation.utils.TestResource;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EvaluationBuilderTest {


    /**
     * 满足以下任一症状时，则符合条件：咳嗽|流涕|咽痛|咳痰|鼻塞|咯血|胸闷|呼吸困难
     */
    @Test
    public void testSyndrome1() {
        evaluation("data/zhenghouqun1Cdc.json", true);
    }

    /**
     *  满足以下任一症状时，则符合条件：腹泻|腹痛|恶心|呕吐|便血
     */

    @Test
    public void testSyndrome2_1() {
        evaluation("data/zhenghouqun2_1Cdc.json", true);
    }

    /**
     * 满足以下任一症状时，则符合条件：腹泻
     */
    @Test
    public void testSyndrome2_2() {
        evaluation("data/zhenghouqun2_1Cdc.json", true);
    }

    /**
     *  发热伴全身症状: 满足以下任一症状时，则符合条件：发热+（皮疹|丘疹|疱疹|斑疹|斑丘疹|环形红斑|皮下瘀点|皮下瘀血|皮下瘀斑）
     */
    @Test
    public void testSyndrome3() {
        evaluation("data/zhenghouqun3Cdc.json", true);
    }

    /**
     * 发热伴皮疹:满足以下任一症状时，则符合条件：发热+（食欲下降|黄疸|畏寒|眼红|结膜充血|结膜下出血|血尿|尿潜血|肌肉酸痛|腓肠肌压痛|多汗|盗汗|乏力|关节痛|淋巴结肿大|肢体活动受限）
     */
    @Test
    public void testSyndrome4() {
        evaluation("data/zhenghouqun4Cdc.json", true);
    }

    /**
     * 发热伴脑膜刺激症候群: 满足以下任一症状时，则符合条件：发热+（喷射性呕吐|头痛|血压下降）
     */

    @Test
    public void testSyndrome5() {
        evaluation("data/zhenghouqun5Cdc.json", true);
    }

    /**
     * 其他发热症候群: 发热，不伴全身症状、呼吸道、消化道、皮疹及脑膜刺激征，且无明确疾病诊断
     */

    @Test
    public void testSyndrome6_1() {
        //不属于其他5种症候群的发热
        evaluation("data/zhenghouqun6_1Cdc.json", true);
    }

    /**
     * 其他发热症候群: 发热+肺部炎性表现或诊断名称（间质性肺炎|肺部感染|白肺|肺炎|肺实变|肺间质炎症）+发绀|气促|胸闷|三凹征|呼吸困难
     */

    @Test
    public void testSyndrome6_2() {
        evaluation("data/zhenghouqun6_2Cdc.json", true);
    }

    /**
     * 其他发热症候群: 发热+死亡
     */
    @Test
    public void testSyndrome6_3() {
        evaluation("data/zhenghouqun6_3Cdc.json", true);
    }

    /**
     * 数据初始化计算样例
     * @param dataFile
     * @param rt
     * @return
     */
    public boolean evaluation(String dataFile, boolean rt) {
        JSONObject jsonObject = null;
        //加载测试数据
        try {
            jsonObject = JSONUtil.parseObj(TestResource.getResource(dataFile));
        } catch (Exception e) {
            e.printStackTrace();
        }
        //解析数据
        JSONArray data = jsonObject.getJSONArray("data");
        Object jObject = jsonObject.get("json");

        System.out.println(JSONUtil.parse(jObject));
        for (Object datum : data) {
            System.out.println(JSONUtil.parse(datum));

            Map<String, Object> dataMap = JSONUtil.toBean(JSONUtil.parseObj(datum), Map.class);
            Map<String, Object> scaleIndexMap = new HashMap<>();
            //初始化数据
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                List<String> values = (List<String>) entry.getValue();
                EvaluationUnit evaluationUnit = new EvaluationUnit();
                List<EvaluationUnitItem> items = new ArrayList<>();
                values.forEach(t -> {
                    EvaluationUnitItem item = new EvaluationUnitItem();
                    item.setValue(t);
                    items.add(item);

                });
                evaluationUnit.setUnitValues(items);
                evaluationUnit.setBasis(true);
                scaleIndexMap.put(entry.getKey(), evaluationUnit);

            }
            //规则计算
            EvaluationBuilder builder = new EvaluationBuilder();
            boolean frt = builder.evaluation(jObject, scaleIndexMap);
            System.out.println(frt);
            if (rt != frt) return false;
        }
        return true;
    }

}