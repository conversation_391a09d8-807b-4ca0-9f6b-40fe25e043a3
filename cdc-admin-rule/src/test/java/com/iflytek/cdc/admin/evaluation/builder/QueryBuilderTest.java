package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONObject;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

public class QueryBuilderTest {

    /**
     * 测试terms规则生成
     */
    @Test
    public void testTermsQueryBuilder() {
        List<String> d = new ArrayList<>();
        d.add("发热");
        d.add("咳嗽");
        JSONObject jsonObject = QueryBuilders.termsQuery("0101", d).toJson();
        System.out.println(jsonObject);
    }

    /**
     * 测试term规则生成
     */
    @Test
    public void testTermQueryBuilder() {
        JSONObject jsonObject = QueryBuilders.termQuery("0101", "发热").toJson();
        System.out.println(jsonObject);
    }

    /**
     * 测试range规则生成
     */
    @Test
    public void testRangeQueryBuilder() {
        JSONObject jsonObject = QueryBuilders.rangeQuery("0101").from(1, true).to(4, false).toJson();
        System.out.println(jsonObject);
    }

    /**
     * 测试exists规则生成
     */
    @Test
    public void testExistsQueryBuilder() {
        List<String> d = new ArrayList<>();
        d.add("发热");
        d.add("咳嗽");
        JSONObject jsonObject = QueryBuilders.existsQuery("0101", d).toJson();
        System.out.println(jsonObject);
    }

    /**
     * 测试regexp规则生成
     */
    @Test
    public void testRegexpQueryBuilder() {
        JSONObject jsonObject = QueryBuilders.regexpQuery("0101", ".*心.*ICU.*").toJson();
        System.out.println(jsonObject);
    }

    /**
     * 测试bool规则生成
     */
    @Test
    public void testBoolQueryBuilder() {
        List<String> d = new ArrayList<>();
        d.add("发热");
        d.add("咳嗽");
        JSONObject jsonObject = QueryBuilders.existsQuery("0101", d).toJson();
        JSONObject jsonObject3 = QueryBuilders.termsQuery("0104", d).toJson();
        JSONObject jsonObject2 = QueryBuilders.termQuery("0101", "发热").toJson();
        JSONObject jsonObjectBool = QueryBuilders.boolQuery().addMust(jsonObject).addMust(jsonObject3).addMustNot(jsonObject2).toJson();
        System.out.println(jsonObjectBool);
    }


}