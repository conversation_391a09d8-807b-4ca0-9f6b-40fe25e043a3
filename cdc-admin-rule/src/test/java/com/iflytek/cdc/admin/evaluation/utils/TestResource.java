package com.iflytek.cdc.admin.evaluation.utils;

import org.apache.commons.io.FileUtils;

/**
 * @Description 获取测试资源
 * <AUTHOR>
 * @Date 2020/6/9
 **/
public class TestResource {


    public static String getResource(String filePath) {


        String jsonData = null;
        try {
            jsonData = FileUtils.readFileToString(FileUtils.toFile(
                    TestResource.class.getResource("/" + filePath)), "UTF-8");
        } catch (
                Exception e) {
            e.printStackTrace();
        }
        return jsonData;
    }

}
